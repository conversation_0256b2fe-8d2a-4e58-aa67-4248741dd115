"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Upload, FileText, Check, X, AlertCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { ApiResponse, MatchingResult } from "@/lib/types/invoice"

// Form schema for invoice
const invoiceFormSchema = z.object({
  file: z.instanceof(File).refine((file) => {
    return file.size > 0;
  }, "Please select a file"),
  vendorName: z.string().optional(),
  invoiceNo: z.string().optional(),
  invoiceDate: z.date().optional(),
  invoiceAmount: z.union([z.number(), z.null()]).optional(),
  vatAmount: z.union([z.number(), z.null()]).optional(),
})

// Form schema for tax invoice
const taxInvoiceFormSchema = z.object({
  file: z.instanceof(File).refine((file) => {
    return file.size > 0;
  }, "Please select a file"),
  vendorName: z.string().optional(),
  taxInvoiceNo: z.string().optional(),
  invoiceDate: z.date().optional(),
  invoiceAmount: z.union([z.number(), z.null()]).optional(),
  vatAmount: z.union([z.number(), z.null()]).optional(),
})

type InvoiceFormValues = z.infer<typeof invoiceFormSchema>
type TaxInvoiceFormValues = z.infer<typeof taxInvoiceFormSchema>

interface InvoiceUploadFormProps {
  onSuccess?: () => void;
}

export function InvoiceUploadForm({ onSuccess }: InvoiceUploadFormProps = {}) {
  const [activeTab, setActiveTab] = React.useState("invoice")
  const [isSubmitting, setIsSubmitting] = React.useState(false)
  const [result, setResult] = React.useState<ApiResponse | null>(null)

  // Form for standard invoice
  const invoiceForm = useForm<InvoiceFormValues>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      vendorName: "",
      invoiceNo: "",
      invoiceAmount: null,
      vatAmount: null,
    },
  })

  // Form for tax invoice
  const taxInvoiceForm = useForm<TaxInvoiceFormValues>({
    resolver: zodResolver(taxInvoiceFormSchema),
    defaultValues: {
      vendorName: "",
      taxInvoiceNo: "",
      invoiceAmount: null,
      vatAmount: null,
    },
  })

  // Handle invoice form submission
  async function onInvoiceSubmit(data: InvoiceFormValues) {
    setIsSubmitting(true)
    setResult(null)

    try {
      const formData = new FormData()
      formData.append("file", data.file)

      // Add input fields individually
      if (data.vendorName) {
        formData.append("vendor_name", data.vendorName)
      }

      if (data.invoiceNo) {
        formData.append("invoice_number", data.invoiceNo)
      }

      if (data.invoiceDate) {
        formData.append("invoice_date", format(data.invoiceDate, "yyyy-MM-dd"))
      }

      if (data.invoiceAmount) {
        formData.append("invoice_amount", data.invoiceAmount.toString())
      }

      if (data.vatAmount) {
        formData.append("vat_amount", data.vatAmount.toString())
      }

      const response = await fetch("/api/process-invoice-file", {
        method: "POST",
        body: formData,
        credentials: "include", // Include cookies for session authentication
      })

      const responseData = await response.json()

      if (!response.ok) {
        // Handle API errors (including 401 Unauthorized)
        throw new Error(responseData.error || `API error: ${response.status} ${response.statusText}`)
      }

      // Ensure the response has the expected structure
      const apiResponse: ApiResponse = {
        ...responseData,
        // Map response fields to expected structure if needed
        log_id: responseData.log_id || responseData.id || responseData.request_id || undefined,
        processing_id: responseData.processing_id || responseData.id || undefined,
        results: responseData.results || responseData.data || { success: true }
      }

      setResult(apiResponse)

      // Call onSuccess callback if provided and the request was successful
      if (onSuccess && apiResponse.results?.success) {
        onSuccess()
      }
    } catch (error) {
      console.error("Error submitting invoice:", error)
      setResult({
        processing_id: `error-${Date.now()}`,
        results: {
          success: false,
          summary: {
            total_fields: 0,
            matched: 0,
            mismatched: 0,
            not_found: 0
          },
          fields: {
            vendor_name: { input_value: '', ocr_value: '', status: 'not_found' },
            invoice_number: { input_value: '', ocr_value: '', status: 'not_found' },
            invoice_date: { input_value: '', ocr_value: '', status: 'not_found' },
            invoice_amount: { input_value: '', ocr_value: '', status: 'not_found' },
            vat_amount: { input_value: '', ocr_value: '', status: 'not_found' }
          }
        },
        error: error instanceof Error ? error.message : "Unknown error",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle tax invoice form submission
  async function onTaxInvoiceSubmit(data: TaxInvoiceFormValues) {
    setIsSubmitting(true)
    setResult(null)

    try {
      const formData = new FormData()
      formData.append("file", data.file)

      // Add input fields individually
      if (data.vendorName) {
        formData.append("vendor_name", data.vendorName)
      }

      if (data.taxInvoiceNo) {
        formData.append("tax_invoice_number", data.taxInvoiceNo)
      }

      if (data.invoiceDate) {
        formData.append("tax_invoice_date", format(data.invoiceDate, "yyyy-MM-dd"))
      }

      if (data.invoiceAmount) {
        formData.append("invoice_amount", data.invoiceAmount.toString())
      }

      if (data.vatAmount) {
        formData.append("vat_amount", data.vatAmount.toString())
      }

      const response = await fetch("/api/process-tax-invoice-file", {
        method: "POST",
        body: formData,
        credentials: "include", // Include cookies for session authentication
      })

      const responseData = await response.json()

      if (!response.ok) {
        // Handle API errors (including 401 Unauthorized)
        throw new Error(responseData.error || `API error: ${response.status} ${response.statusText}`)
      }

      const apiResponse = responseData as ApiResponse
      setResult(apiResponse)

      // Call onSuccess callback if provided and the request was successful
      if (onSuccess && apiResponse.results?.success) {
        onSuccess()
      }
    } catch (error) {
      console.error("Error submitting tax invoice:", error)
      setResult({
        processing_id: `error-${Date.now()}`,
        results: {
          success: false,
          summary: {
            total_fields: 0,
            matched: 0,
            mismatched: 0,
            not_found: 0
          },
          fields: {
            vendor_name: { input_value: '', ocr_value: '', status: 'not_found' },
            tax_invoice_number: { input_value: '', ocr_value: '', status: 'not_found' },
            tax_invoice_date: { input_value: '', ocr_value: '', status: 'not_found' },
            invoice_amount: { input_value: '', ocr_value: '', status: 'not_found' },
            vat_amount: { input_value: '', ocr_value: '', status: 'not_found' }
          }
        },
        error: error instanceof Error ? error.message : "Unknown error",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset forms when switching tabs
  React.useEffect(() => {
    setResult(null)
    if (activeTab === "invoice") {
      invoiceForm.reset()
    } else {
      taxInvoiceForm.reset()
    }
  }, [activeTab, invoiceForm, taxInvoiceForm])

  return (
    <div className="w-full max-w-3xl mx-auto">
      <Tabs defaultValue="invoice" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="invoice">
            <FileText className="mr-2 h-4 w-4" />
            Standard Invoice
          </TabsTrigger>
          <TabsTrigger value="taxInvoice">
            <FileText className="mr-2 h-4 w-4" />
            Tax Invoice
          </TabsTrigger>
        </TabsList>

        {/* Standard Invoice Form */}
        <TabsContent value="invoice">
          <Card>
            <CardHeader>
              <CardTitle>Upload Invoice</CardTitle>
              <CardDescription>
                Upload a PDF invoice and provide optional matching data.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...invoiceForm}>
                <form onSubmit={invoiceForm.handleSubmit(onInvoiceSubmit)} className="space-y-6">
                  <FormField
                    control={invoiceForm.control}
                    name="file"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice PDF</FormLabel>
                        <FormControl>
                          <Input
                            type="file"
                            accept=".pdf"
                            onChange={(e) => {
                              const file = e.target.files?.[0]
                              if (file) {
                                field.onChange(file)
                              }
                            }}
                            // Don't pass value to file inputs
                            onBlur={field.onBlur}
                            name={field.name}
                            ref={field.ref}
                          />
                        </FormControl>
                        <FormDescription>
                          Upload a PDF file of the invoice.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={invoiceForm.control}
                      name="vendorName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Vendor Name</FormLabel>
                          <FormControl>
                            <Input placeholder="PT Danantara Makmur" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={invoiceForm.control}
                      name="invoiceNo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Invoice Number</FormLabel>
                          <FormControl>
                            <Input placeholder="INV-2023-001" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={invoiceForm.control}
                      name="invoiceDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Invoice Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "pl-3 text-left font-normal h-10",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date > new Date() || date < new Date("1900-01-01")
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={invoiceForm.control}
                      name="invoiceAmount"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Invoice Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              value={field.value === null ? '' : field.value}
                              onChange={(e) => {
                                const value = e.target.value === '' ? null : parseFloat(e.target.value);
                                field.onChange(value);
                              }}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={invoiceForm.control}
                      name="vatAmount"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>VAT Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              value={field.value === null ? '' : field.value}
                              onChange={(e) => {
                                const value = e.target.value === '' ? null : parseFloat(e.target.value);
                                field.onChange(value);
                              }}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button type="submit" className="w-full" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        Upload and Process
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tax Invoice Form */}
        <TabsContent value="taxInvoice">
          <Card>
            <CardHeader>
              <CardTitle>Upload Tax Invoice</CardTitle>
              <CardDescription>
                Upload a PDF tax invoice and provide optional matching data.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...taxInvoiceForm}>
                <form onSubmit={taxInvoiceForm.handleSubmit(onTaxInvoiceSubmit)} className="space-y-6">
                  <FormField
                    control={taxInvoiceForm.control}
                    name="file"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Invoice PDF</FormLabel>
                        <FormControl>
                          <Input
                            type="file"
                            accept=".pdf"
                            onChange={(e) => {
                              const file = e.target.files?.[0]
                              if (file) {
                                field.onChange(file)
                              }
                            }}
                            // Don't pass value to file inputs
                            onBlur={field.onBlur}
                            name={field.name}
                            ref={field.ref}
                          />
                        </FormControl>
                        <FormDescription>
                          Upload a PDF file of the tax invoice.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={taxInvoiceForm.control}
                      name="vendorName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Vendor Name</FormLabel>
                          <FormControl>
                            <Input placeholder="PT Danantara Makmur" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={taxInvoiceForm.control}
                      name="taxInvoiceNo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tax Invoice Number</FormLabel>
                          <FormControl>
                            <Input placeholder="TAX-2023-001" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={taxInvoiceForm.control}
                      name="invoiceDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Invoice Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "pl-3 text-left font-normal h-10",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date > new Date() || date < new Date("1900-01-01")
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={taxInvoiceForm.control}
                      name="invoiceAmount"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Invoice Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              value={field.value === null ? '' : field.value}
                              onChange={(e) => {
                                const value = e.target.value === '' ? null : parseFloat(e.target.value);
                                field.onChange(value);
                              }}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={taxInvoiceForm.control}
                      name="vatAmount"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>VAT Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              value={field.value === null ? '' : field.value}
                              onChange={(e) => {
                                const value = e.target.value === '' ? null : parseFloat(e.target.value);
                                field.onChange(value);
                              }}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button type="submit" className="w-full" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        Upload and Process
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* External API Usage Message */}
      <div className="mt-4 text-sm text-muted-foreground">
        <p className="mb-2">
          <strong>Note:</strong> For external API access, you'll need to create an API key in the{" "}
          <a href="/admin?tab=api" className="text-primary underline">API Documentation</a> tab.
        </p>
        <p>
          When using this form directly, your admin session is used for authentication.
        </p>
      </div>

      {/* Results Display */}
      {result && (
        <div className="mt-8">
          <Alert variant={result.error ? "destructive" : "default"}>
            <div className="flex items-center">
              {result.error ? (
                <AlertCircle className="h-4 w-4 mr-2" />
              ) : (
                <Check className="h-4 w-4 mr-2" />
              )}
              <AlertTitle>{result.error ? "Error" : "Success"}</AlertTitle>
            </div>
            <AlertDescription>
              {result.error ||
                `${result.log_id ? `Log ID: ${result.log_id}. ` : result.processing_id ? `Processing ID: ${result.processing_id}. ` : ''}
                Matched: ${result.results?.summary?.matched || 0}/${result.results?.summary?.total_fields || 0} fields.`
              }
            </AlertDescription>
          </Alert>

          {result.results && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle>Processing Results</CardTitle>
                <CardDescription>
                  {result.results.is_colored !== undefined && (
                    <div className="text-sm">
                      Invoice is {result.results.is_colored ? "colored" : "not colored"}
                    </div>
                  )}
                  {result.results.summary && (
                    <div className="text-sm mt-1">
                      Summary: {result.results.summary.matched || 0} matched,
                      {result.results.summary.mismatched || 0} mismatched,
                      {result.results.summary.not_found || 0} not found
                    </div>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Field Matching Results</h3>
                  <div className="space-y-2">
                    {result.results.fields && Object.entries(result.results.fields).map(([key, field]) => (
                    <div key={key} className="border rounded-md p-3">
                      <div className="flex items-center mb-2">
                        {field.status === 'matched' ? (
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                        ) : field.status === 'mismatched' ? (
                          <X className="h-4 w-4 text-red-500 mr-2" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-yellow-500 mr-2" />
                        )}
                        <span className="font-medium">{key.replace(/_/g, ' ')}</span>
                        <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-800">
                          {field.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Input:</span>{' '}
                          <span className="font-mono">{field.input_value || 'N/A'}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">OCR:</span>{' '}
                          <span className="font-mono">{field.ocr_value || 'N/A'}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
            {result.log_id && (
              <CardFooter>
                <div className="text-sm text-muted-foreground">
                  Log ID: {result.log_id}
                </div>
              </CardFooter>
            )}
          </Card>
          )}
        </div>
      )}
    </div>
  )
}
