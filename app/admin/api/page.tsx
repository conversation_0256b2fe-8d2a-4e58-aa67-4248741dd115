'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function ApiPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the admin page with the API tab selected
    router.replace('/admin?tab=api');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
    </div>
  );
}
