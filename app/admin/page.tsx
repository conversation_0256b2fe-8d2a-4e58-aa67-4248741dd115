"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import { signOut } from "next-auth/react";
import { Calendar as CalendarIcon, LayoutDashboard, FileText, Upload, Code, Clock } from "lucide-react";
import Link from "next/link";
import { format, startOfMonth, endOfMonth, subMonths } from "date-fns";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LogTable } from "@/components/admin/log-table";
import { ApiKeyManagement } from "@/components/admin/api-key-management";
import { FormattedLogEntry } from "@/lib/types/log";
import { Calendar } from "@/components/ui/calendar";
import {
  Pop<PERSON>,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { InvoiceUploadForm } from "@/components/forms/invoice-upload-form";
import { LogsResponse } from "@/lib/types/log";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

// Import DateRange type from react-day-picker
import { DateRange } from "react-day-picker";

export default function AdminPage() {
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      redirect("/admin/login");
    },
  });

  const handleSignOut = () => {
    signOut({ callbackUrl: "/admin/login" });
  };

  const [logs, setLogs] = React.useState<FormattedLogEntry[]>([])
  const [loading, setLoading] = React.useState(true)
  const [activeTab, setActiveTab] = React.useState("dashboard")
  const [pagination, setPagination] = React.useState({
    page: 1,
    pageSize: 10,
    totalPages: 1,
    total: 0,
  })
  const [filters, setFilters] = React.useState({
    requestType: "",
    processingStatus: "",
    vendorName: "",
    documentNo: "",
  })
  // Time window options
  type TimeWindow = "current-month" | "last-month" | "custom";

  // Initialize with "current-month" as default
  const [timeWindow, setTimeWindow] = React.useState<TimeWindow>("current-month");

  // State to control date picker popover
  const [datePickerOpen, setDatePickerOpen] = React.useState(false);

  // Calculate initial date range for current month
  const today = new Date();
  const firstDayOfMonth = startOfMonth(today);

  const [dateRange, setDateRange] = React.useState<DateRange | undefined>({
    from: firstDayOfMonth,
    to: today,
  })

  // Function to calculate date ranges for predefined time windows
  const calculateDateRange = (window: TimeWindow): DateRange => {
    const today = new Date();

    switch (window) {
      case "current-month":
        return {
          from: startOfMonth(today),
          to: today
        };
      case "last-month":
        const lastMonth = subMonths(today, 1);
        return {
          from: startOfMonth(lastMonth),
          to: endOfMonth(lastMonth)
        };
      case "custom":
      default:
        return dateRange || { from: undefined, to: undefined };
    }
  }

  // Stats
  const [stats, setStats] = React.useState({
    total: 0,
    success: 0,
    failed: 0,
    processing: 0,
    invoices: 0,
    taxInvoices: 0,
    totalPages: 0,
    invoicePages: 0,
    taxInvoicePages: 0,
  })

  // Fetch logs from the API
  const fetchLogs = React.useCallback(async () => {
    setLoading(true)
    try {
      // Build query parameters for the API request
      const queryParams = new URLSearchParams();

      if (filters.requestType) {
        queryParams.append('requestType', filters.requestType);
      }

      if (filters.processingStatus) {
        queryParams.append('processingStatus', filters.processingStatus);
      }

      if (filters.vendorName) {
        queryParams.append('vendorName', filters.vendorName);
      }

      if (filters.documentNo) {
        queryParams.append('documentNo', filters.documentNo);
      }

      if (dateRange?.from) {
        queryParams.append('startDate', dateRange.from.toISOString());

        if (dateRange.to) {
          queryParams.append('endDate', dateRange.to.toISOString());
        }
      }

      // Add pagination parameters
      queryParams.append('page', pagination.page.toString());
      queryParams.append('pageSize', pagination.pageSize.toString());

      // Make the API request
      const response = await fetch(`/api/logs?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json() as LogsResponse;

      // Set logs from the API response
      setLogs(data.logs);

      // Update pagination information
      setPagination({
        page: data.page,
        pageSize: data.pageSize,
        totalPages: data.totalPages,
        total: data.total,
      });

      // Make a separate request to get all logs for stats (without pagination)
      const statsQueryParams = new URLSearchParams();
      if (filters.requestType) statsQueryParams.append('requestType', filters.requestType);
      if (filters.processingStatus) statsQueryParams.append('processingStatus', filters.processingStatus);
      if (filters.vendorName) statsQueryParams.append('vendorName', filters.vendorName);
      if (filters.documentNo) statsQueryParams.append('documentNo', filters.documentNo);

      if (dateRange?.from) {
        statsQueryParams.append('startDate', dateRange.from.toISOString());
        if (dateRange.to) {
          statsQueryParams.append('endDate', dateRange.to.toISOString());
        }
      }

      // Set page size to a large number to get all logs for stats
      statsQueryParams.append('pageSize', '1000');

      const statsResponse = await fetch(`/api/logs?${statsQueryParams.toString()}`);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json() as LogsResponse;
        const allLogs = statsData.logs;
        const successLogs = allLogs.filter(log => log.processingStatus === "success").length;
        const failedLogs = allLogs.filter(log => log.processingStatus === "failed").length;
        const processingLogs = allLogs.filter(log => log.processingStatus === "processing").length;
        const invoiceLogs = allLogs.filter(log => log.requestType === "invoice").length;
        const taxInvoiceLogs = allLogs.filter(log => log.requestType === "taxInvoice").length;

        // Calculate page metrics
        const totalPages = allLogs.reduce((sum, log) => sum + (log.totalPages || 0), 0);
        const invoicePages = allLogs
          .filter(log => log.requestType === "invoice")
          .reduce((sum, log) => sum + (log.totalPages || 0), 0);
        const taxInvoicePages = allLogs
          .filter(log => log.requestType === "taxInvoice")
          .reduce((sum, log) => sum + (log.totalPages || 0), 0);

        setStats({
          total: statsData.total,
          success: successLogs,
          failed: failedLogs,
          processing: processingLogs,
          invoices: invoiceLogs,
          taxInvoices: taxInvoiceLogs,
          totalPages,
          invoicePages,
          taxInvoicePages,
        });
      } else {
        // If stats request fails, use the paginated data for stats
        const allLogs = data.logs;
        const successLogs = allLogs.filter(log => log.processingStatus === "success").length;
        const failedLogs = allLogs.filter(log => log.processingStatus === "failed").length;
        const processingLogs = allLogs.filter(log => log.processingStatus === "processing").length;
        const invoiceLogs = allLogs.filter(log => log.requestType === "invoice").length;
        const taxInvoiceLogs = allLogs.filter(log => log.requestType === "taxInvoice").length;

        // Calculate page metrics
        const totalPages = allLogs.reduce((sum, log) => sum + (log.totalPages || 0), 0);
        const invoicePages = allLogs
          .filter(log => log.requestType === "invoice")
          .reduce((sum, log) => sum + (log.totalPages || 0), 0);
        const taxInvoicePages = allLogs
          .filter(log => log.requestType === "taxInvoice")
          .reduce((sum, log) => sum + (log.totalPages || 0), 0);

        setStats({
          total: data.total,
          success: successLogs,
          failed: failedLogs,
          processing: processingLogs,
          invoices: invoiceLogs,
          taxInvoices: taxInvoiceLogs,
          totalPages,
          invoicePages,
          taxInvoicePages,
        });
      }

    } catch (error) {
      console.error("Error fetching logs:", error);
    } finally {
      setLoading(false);
    }
  }, [filters, dateRange, pagination.page, pagination.pageSize]);

  // Check for tab and time window query parameters on initial load
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);

      // Handle tab parameter
      const tabParam = params.get('tab');
      if (tabParam && ['dashboard', 'logs', 'upload', 'api'].includes(tabParam)) {
        setActiveTab(tabParam);
      }

      // Handle date range parameters for all tabs
      const fromParam = params.get('from');
      const toParam = params.get('to');

      if (fromParam) {
        try {
          const fromDate = new Date(fromParam);
          const toDate = toParam ? new Date(toParam) : new Date();
          setDateRange({
            from: fromDate,
            to: toDate
          });
        } catch (e) {
          console.error("Invalid date format in URL parameters");
          // Use default date range
          setDateRange({
            from: firstDayOfMonth,
            to: today
          });
        }
      } else {
        // If no date parameters, use current month as default
        setDateRange({
          from: firstDayOfMonth,
          to: today
        });
      }

      // Handle time window parameter (only for dashboard tab)
      if (tabParam === 'dashboard') {
        const windowParam = params.get('timeWindow') as TimeWindow | null;
        if (windowParam && ['current-month', 'last-month', 'custom'].includes(windowParam)) {
          // Set the time window state for dashboard
          setTimeWindow(windowParam);

          if (windowParam === 'custom' && !fromParam) {
            // For custom time window without date params, clear the date range
            setDateRange({ from: undefined, to: undefined });
            // Open the date picker when custom is selected
            setDatePickerOpen(true);
          } else if (windowParam !== 'custom') {
            // For predefined windows, calculate the date range
            const newDateRange = calculateDateRange(windowParam);
            setDateRange(newDateRange);
          }
        } else {
          // If no time window parameter, use current month as default for dashboard
          setTimeWindow('current-month');
        }
      }
    }
  }, []);

  // Fetch logs when component mounts or when filters/pagination change
  React.useEffect(() => {
    fetchLogs()
  }, [fetchLogs])

  // Refresh logs when switching to dashboard or logs tab
  React.useEffect(() => {
    if (activeTab === 'dashboard' || activeTab === 'logs') {
      fetchLogs();
    }
  }, [activeTab, fetchLogs])

  const handleFilterChange = (newFilters: { [key: string]: string }) => {
    // Reset to page 1 when filters change
    setPagination(prev => ({ ...prev, page: 1 }));

    // Handle date range filters from LogTable
    if ('startDate' in newFilters || 'endDate' in newFilters) {
      if (newFilters.startDate && newFilters.endDate) {
        try {
          const fromDate = new Date(newFilters.startDate);
          const toDate = new Date(newFilters.endDate);

          // Only set date range if both dates are provided
          setDateRange({
            from: fromDate,
            to: toDate
          });

          // Update URL with date parameters
          if (typeof window !== 'undefined') {
            const url = new URL(window.location.href);
            url.searchParams.set('from', fromDate.toISOString());
            url.searchParams.set('to', toDate.toISOString());
            window.history.pushState({}, '', url.toString());
          }
        } catch (e) {
          console.error("Invalid date format:", e);
        }
      } else if (!newFilters.startDate && !newFilters.endDate) {
        // Clear date range
        setDateRange(undefined);

        // Update URL to remove date parameters
        if (typeof window !== 'undefined') {
          const url = new URL(window.location.href);
          url.searchParams.delete('from');
          url.searchParams.delete('to');
          window.history.pushState({}, '', url.toString());
        }
      }
    }

    setFilters(prev => ({ ...prev, ...newFilters }));
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  }

  // Handle time window changes (only for Dashboard tab)
  const handleTimeWindowChange = (selectedWindow: TimeWindow) => {
    // Only apply time window changes if we're on the dashboard tab
    if (activeTab !== 'dashboard') return;

    // Update the time window state
    setTimeWindow(selectedWindow);

    // Update URL with the time window parameter
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('timeWindow', selectedWindow);

      if (selectedWindow === 'custom') {
        // For custom time window, always clear the date range to allow fresh selection
        setDateRange({ from: undefined, to: undefined });

        // Open the date picker when custom is selected
        setDatePickerOpen(true);

        // Clear any existing date parameters in the URL
        url.searchParams.delete('from');
        url.searchParams.delete('to');
      } else {
        // For predefined time windows, calculate the date range and update it
        const newDateRange = calculateDateRange(selectedWindow);
        setDateRange(newDateRange);

        // Update URL with date parameters
        if (newDateRange.from) {
          url.searchParams.set('from', newDateRange.from.toISOString());
          if (newDateRange.to) {
            url.searchParams.set('to', newDateRange.to.toISOString());
          } else {
            url.searchParams.delete('to');
          }
        }
      }

      // Update URL without full page reload
      window.history.pushState({}, '', url.toString());

      // Reset to page 1 when time window changes
      setPagination(prev => ({ ...prev, page: 1 }));
    }
  }

  // Handle date range changes
  const onDateRangeChange = (range: { from: Date | undefined; to?: Date | undefined }) => {
    setDateRange(range);

    // When date range changes manually in dashboard tab, set time window to custom
    if (activeTab === 'dashboard') {
      setTimeWindow('custom');
    }

    // Close the date picker when both from and to dates are selected
    if (range.from && range.to) {
      // Use a small timeout to allow the UI to update before closing
      setTimeout(() => {
        setDatePickerOpen(false);
      }, 100);
    }

    // Update URL with date parameters
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);

      // Only set timeWindow parameter if we're on the dashboard tab
      if (activeTab === 'dashboard') {
        url.searchParams.set('timeWindow', 'custom');
      }

      if (range.from) {
        url.searchParams.set('from', range.from.toISOString());
        if (range.to) {
          url.searchParams.set('to', range.to.toISOString());
        } else {
          url.searchParams.delete('to');
        }
      } else {
        url.searchParams.delete('from');
        url.searchParams.delete('to');
      }

      // Update URL without full page reload
      window.history.pushState({}, '', url.toString());
    }

    // Reset to page 1 when date range changes
    setPagination(prev => ({ ...prev, page: 1 }));

    // Refresh data with new date range
    fetchLogs();
  };

  return (
    <div className="min-h-screen p-8 bg-background text-foreground">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Invoice Matching Admin</h2>
        <div className="flex items-center space-x-2">
          <Button onClick={handleSignOut} variant="outline" size="sm">
            Sign Out
          </Button>
        </div>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={(value) => {
          setActiveTab(value);
          // Update URL with tab parameter without full page reload
          const url = new URL(window.location.href);
          url.searchParams.set('tab', value);
          window.history.pushState({}, '', url.toString());
        }}
        className="space-y-4 mt-6">
        <TabsList>
          <TabsTrigger value="dashboard">
            <LayoutDashboard className="mr-2 h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="logs">
            <FileText className="mr-2 h-4 w-4" />
            Processing Logs
          </TabsTrigger>
          <TabsTrigger value="upload">
            <Upload className="mr-2 h-4 w-4" />
            Upload Invoice
          </TabsTrigger>
          <TabsTrigger value="api">
            <Code className="mr-2 h-4 w-4" />
            API Documentation
          </TabsTrigger>
        </TabsList>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Dashboard</h3>
            <div className="flex items-center gap-4">
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Time Window:</span>
              </div>
              <ToggleGroup
                type="single"
                value={timeWindow}
                onValueChange={(value: TimeWindow) => {
                  if (value) {
                    handleTimeWindowChange(value);
                  }
                }}
                className="border rounded-md"
              >
                <ToggleGroupItem value="current-month" aria-label="Current Month" className="px-3">
                  Current Month
                </ToggleGroupItem>
                <ToggleGroupItem value="last-month" aria-label="Last Month" className="px-3">
                  Last Month
                </ToggleGroupItem>

                {/* Custom toggle with integrated date picker */}
                <Popover
                  open={timeWindow === 'custom' && datePickerOpen}
                  onOpenChange={(open) => {
                    setDatePickerOpen(open);
                    if (open && timeWindow !== 'custom') {
                      handleTimeWindowChange('custom');
                    }
                  }}
                >
                  <PopoverTrigger asChild>
                    <ToggleGroupItem
                      value="custom"
                      aria-label="Custom Range"
                      className={cn(
                        "px-3",
                        dateRange?.from && dateRange?.to && timeWindow === 'custom' && "flex items-center gap-2"
                      )}
                      onClick={() => {
                        if (timeWindow === 'custom') {
                          setDatePickerOpen(!datePickerOpen);
                        }
                      }}
                    >
                      {timeWindow === 'custom' && dateRange?.from && dateRange?.to ? (
                        <>
                          <span>Custom:</span>
                          <span className="text-xs font-normal">
                            {format(dateRange.from, "MMM d")} - {format(dateRange.to, "MMM d")}
                          </span>
                        </>
                      ) : (
                        "Custom"
                      )}
                    </ToggleGroupItem>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-auto p-0"
                    align="end"
                    alignOffset={-10}
                    sideOffset={5}
                  >
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange?.from || new Date()}
                      selected={dateRange}
                      onSelect={(range) => {
                        if (range) {
                          onDateRangeChange({
                            from: range.from,
                            to: range.to
                          });

                          // Close the date picker when both from and to dates are selected
                          if (range.from && range.to) {
                            setTimeout(() => {
                              setDatePickerOpen(false);
                            }, 100);
                          }
                        } else {
                          onDateRangeChange({
                            from: undefined,
                            to: undefined
                          });
                        }
                      }}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </ToggleGroup>

            </div>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Processed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
                <p className="text-xs text-muted-foreground">All time</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0}%
                </div>
                <p className="text-xs text-muted-foreground">{stats.success} successful</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Invoices</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.invoices}</div>
                <p className="text-xs text-muted-foreground">Standard invoices</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tax Invoices</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.taxInvoices}</div>
                <p className="text-xs text-muted-foreground">Tax invoices</p>
              </CardContent>
            </Card>
          </div>

          <h3 className="text-lg font-medium mt-6 mb-2">Page Metrics</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Pages Processed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalPages}</div>
                <p className="text-xs text-muted-foreground">All documents</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Invoice Pages</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.invoicePages}</div>
                <p className="text-xs text-muted-foreground">Standard invoice documents</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tax Invoice Pages</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.taxInvoicePages}</div>
                <p className="text-xs text-muted-foreground">Tax invoice documents</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Logs Tab */}
        <TabsContent value="logs" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Processing Logs</h3>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => fetchLogs()}
                disabled={loading}
              >
                {loading ? (
                  <span className="flex items-center">
                    <span className="animate-spin mr-2 h-4 w-4 border-b-2 border-current rounded-full"></span>
                    Refreshing...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21.5 2v6h-6M21.34 15.57a10 10 0 1 1-.57-8.38" />
                    </svg>
                    Refresh
                  </span>
                )}
              </Button>
              <Button onClick={() => {
                // Clear date range
                setDateRange(undefined);

                // Clear all text filters
                setFilters({
                  requestType: "",
                  processingStatus: "",
                  vendorName: "",
                  documentNo: "",
                });

                // Update URL to remove filter parameters
                if (typeof window !== 'undefined') {
                  const url = new URL(window.location.href);

                  // Remove date parameters
                  url.searchParams.delete('from');
                  url.searchParams.delete('to');

                  // Remove any other filter parameters
                  url.searchParams.delete('timeWindow');

                  window.history.pushState({}, '', url.toString());
                }

                // Reset to page 1 when filters are cleared
                setPagination(prev => ({ ...prev, page: 1 }));

                // Refresh logs with cleared filters
                fetchLogs();
              }}>
                Clear All Filters
              </Button>
            </div>
          </div>



          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <>
              <LogTable
                data={logs}
                onFilterChange={handleFilterChange}
                currentFilters={filters}
                dateRange={dateRange || { from: undefined, to: undefined }}
                showDateRangePicker={false}
              />
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Showing {logs.length > 0 ? (pagination.page - 1) * pagination.pageSize + 1 : 0} to {Math.min(pagination.page * pagination.pageSize, pagination.total)} of {pagination.total} entries
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page <= 1}
                  >
                    Previous
                  </Button>
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    // Show pages around the current page
                    let pageToShow = pagination.page - 2 + i;

                    // Adjust if we're at the beginning
                    if (pagination.page < 3) {
                      pageToShow = i + 1;
                    }

                    // Adjust if we're at the end
                    if (pagination.page > pagination.totalPages - 2) {
                      pageToShow = pagination.totalPages - 4 + i;
                      if (pageToShow < 1) pageToShow = i + 1;
                    }

                    // Only show valid page numbers
                    if (pageToShow > 0 && pageToShow <= pagination.totalPages) {
                      return (
                        <Button
                          key={pageToShow}
                          variant={pagination.page === pageToShow ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pageToShow)}
                        >
                          {pageToShow}
                        </Button>
                      );
                    }
                    return null;
                  })}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page >= pagination.totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </>
          )}
        </TabsContent>

        {/* Upload Invoice Tab */}
        <TabsContent value="upload" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Upload Invoice</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1">
              <div className="rounded-lg border bg-card p-6">
                <h3 className="font-semibold text-lg mb-2">Instructions</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Upload a PDF invoice and provide optional matching data for processing.
                </p>
                <div className="space-y-4 text-sm">
                  <div>
                    <h4 className="font-medium">Supported Fields</h4>
                    <ul className="mt-1 space-y-1">
                      <li>Vendor Name</li>
                      <li>Invoice/Tax Invoice Number</li>
                      <li>Invoice Date</li>
                      <li>Total Amount</li>
                      <li>Total VAT Amount</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div className="md:col-span-2">
              <InvoiceUploadForm onSuccess={fetchLogs} />
            </div>
          </div>
        </TabsContent>

        {/* API Documentation Tab */}
        <TabsContent value="api" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">API Documentation</h3>
          </div>

          <div className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Code className="mr-2 h-5 w-5" />
                  API Documentation Tools
                </CardTitle>
                <CardDescription>
                  Interactive documentation and client integration tools for the Invoice Matching API
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Swagger UI</h3>
                    <p className="mb-4">
                      Swagger UI allows you to interact with the API directly from the documentation, making it easy to test endpoints and see responses.
                    </p>
                    <Button asChild>
                      <Link href="/admin/swagger" target="_blank">
                        <Code className="mr-2 h-4 w-4" />
                        Open Swagger UI
                      </Link>
                    </Button>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium mb-2">Postman Collection</h3>
                    <p className="mb-4">
                      Download a ready-to-use Postman collection with pre-configured requests for all API endpoints, including examples and authentication settings.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Button asChild>
                        <Link href="/api/download/postman-collection">
                          <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                            <polyline points="7 10 12 15 17 10" />
                            <line x1="12" y1="15" x2="12" y2="3" />
                          </svg>
                          Postman Collection
                        </Link>
                      </Button>
                      <Button asChild>
                        <Link href="/api/download/postman-environment">
                          <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                            <polyline points="7 10 12 15 17 10" />
                            <line x1="12" y1="15" x2="12" y2="3" />
                          </svg>
                          Postman Environment
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>API Key Management</CardTitle>
                <CardDescription>
                  Create and manage API keys for accessing the Invoice Matching API
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ApiKeyManagement />
              </CardContent>
            </Card>
          </div>

          <div className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>API Overview</CardTitle>
                <CardDescription>
                  The Invoice Matching API provides endpoints for processing invoices and retrieving logs. Use the Swagger UI or Postman Collection for detailed documentation.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Authentication</h3>
                    <p className="text-sm text-muted-foreground">
                      All API endpoints require an API key for authentication. The API key should be included in the request headers as <code>X-API-Key</code>.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">Available Endpoints</h3>
                    <ul className="list-disc pl-6 space-y-2 text-sm text-muted-foreground">
                      <li>
                        <strong>POST /api/process-invoice</strong> - Process a standard invoice from URL and match against provided data
                      </li>
                      <li>
                        <strong>POST /api/process-invoice-file</strong> - Upload and process a standard invoice file and match against provided data
                      </li>
                      <li>
                        <strong>POST /api/process-tax-invoice</strong> - Process a tax invoice from URL and match against provided data
                      </li>
                      <li>
                        <strong>POST /api/process-tax-invoice-file</strong> - Upload and process a tax invoice file and match against provided data
                      </li>
                      <li>
                        <strong>GET /api/logs</strong> - Retrieve processing logs with optional filtering and pagination
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">Response Format</h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      All API responses are returned in JSON format with a consistent structure:
                    </p>
                    <pre className="bg-muted p-4 rounded-md text-xs overflow-auto">
{`// Success response example
{
  "processing_id": "unique-id-123",
  "results": {
    "success": true,
    "summary": {
      "total_fields": 5,
      "matched": 3,
      "mismatched": 1,
      "not_found": 1
    },
    "fields": {
      // Field matching details
    },
    "is_colored": false,
    "color_pages": [],
    "total_pages": 3
  },
  "log_id": "log-entry-id"
}`}
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
