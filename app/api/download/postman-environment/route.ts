import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(req: NextRequest) {
  try {
    // Get the file path
    const filePath = path.join(process.cwd(), 'public', 'postman', 'invoice-matching-environment.json');
    
    // Read the file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    // Create a response with the file content and appropriate headers
    const response = new NextResponse(fileContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': 'attachment; filename="invoice-matching-environment.json"',
      },
    });
    
    return response;
  } catch (error) {
    console.error('Error serving Postman environment:', error);
    return new NextResponse(JSON.stringify({ error: 'Failed to download file' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}
