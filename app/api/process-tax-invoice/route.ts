import { NextRequest, NextResponse } from 'next/server';
import { TaxInvoiceData } from '@/lib/types/invoice';
import { processTaxInvoice } from '@/lib/api/external-service';
import { validateApi<PERSON><PERSON>, ApiKeyValidationResult } from '@/lib/api/api-key-middleware';
import {
  createTaxInvoiceErrorResponse,
  createUrlProcessingLog,
  updateTaxInvoiceLogWithResults,
  updateLogWithError,
  createApiResponse,
  extractTotalPages,
  getTotalPagesFromUrl,
  updateLogWithApiKeyId
} from '@/lib/api/shared-invoice-processing';

// No need to fetch PDF for color detection in tax invoice endpoint

/**
 * Process and match a tax invoice from a URL
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  // Validate API key
  const validation = await validateApiKey(req);

  // Handle both old and new API key middleware behavior
  if (validation === null) {
    // Old behavior - null means valid
    return null;
  } else if (validation && 'error' in validation && validation.error) {
    // New behavior - error property means invalid
    return validation.error;
  }

  // New behavior - valid with API key ID or empty object for session auth

  try {
    // Parse the JSON body
    const body = await req.json();

    // Parse and coerce types before validation
    const parsedBody = {
      ...body,
      invoice_amount: typeof body.invoice_amount === 'string' ? parseFloat(body.invoice_amount) : body.invoice_amount,
      vat_amount: typeof body.vat_amount === 'string' ? parseFloat(body.vat_amount) : body.vat_amount,
    };
    // Validate required parameters using shared utility
    const requiredFields = [
      { key: 'file_url', name: 'file_url', type: 'string' },
      { key: 'vendor_name', name: 'vendor_name', type: 'string' },
      { key: 'tax_invoice_number', name: 'tax_invoice_number', type: 'string' },
      { key: 'tax_invoice_date', name: 'tax_invoice_date', type: 'date' },
      { key: 'invoice_amount', name: 'invoice_amount', type: 'number' },
      { key: 'vat_amount', name: 'vat_amount', type: 'number' },
    ];
    const { validateInvoiceFields } = await import('@/lib/api/validate-invoice-fields');
    const validationError = validateInvoiceFields(parsedBody, requiredFields);
    if (validationError) {
      return NextResponse.json(
        {
          error: validationError,
          results: { success: false }
        },
        { status: 400 }
      );
    }

    // Extract input data from request body
    const fileUrl = body.file_url;
    const inputData: TaxInvoiceData = {
      vendorName: body.vendor_name,
      taxInvoiceNo: body.tax_invoice_number,
      invoiceDate: body.tax_invoice_date,
      invoiceAmount: parseFloat(body.invoice_amount),
      vatAmount: parseFloat(body.vat_amount),
    };


    // Create a log entry for this request
    const log = await createUrlProcessingLog(
      'taxInvoice',
      fileUrl,
      inputData
    );

    // If we have an API key ID, update the log with it
    if (validation && 'apiKeyId' in validation && validation.apiKeyId) {
      await updateLogWithApiKeyId(log.id, validation.apiKeyId);
    }

    try {
      // Start processing the tax invoice and page count in parallel
      const externalResponsePromise = processTaxInvoice(fileUrl, inputData);
      const pageCountPromise = getTotalPagesFromUrl(fileUrl);

      // Wait for the tax invoice processing to complete
      const externalResponse = await externalResponsePromise;

      // Use shared matching utility for robust field comparison
      const { matchFields } = await import('@/lib/api/field-matching');
      const fieldTypes = {
        vendor_name: 'string',
        tax_invoice_number: 'string',
        tax_invoice_date: 'date',
        invoice_amount: 'number',
        vat_amount: 'number',
      };
      const { fields: enhancedFields, summary: enhancedSummary } = matchFields(
        {
          vendor_name: inputData.vendorName,
          tax_invoice_number: inputData.taxInvoiceNo,
          tax_invoice_date: inputData.invoiceDate,
          invoice_amount: inputData.invoiceAmount,
          vat_amount: inputData.vatAmount,
        } as Record<string, string | number | Date>,
        externalResponse.results.fields,
        fieldTypes
      );
      // Get the page count from our PDF utility
      const pageCount = await pageCountPromise;
      // Use our page count, or fall back to DONA's total_pages if our count fails
      const totalPages = pageCount > 0 ? pageCount : extractTotalPages(externalResponse);
      // Add total_pages to the results
      const enhancedResults = {
        ...externalResponse.results,
        fields: enhancedFields,
        summary: enhancedSummary,
        total_pages: totalPages
      };

      // Update the log with the results
      await updateTaxInvoiceLogWithResults(log.id, externalResponse, enhancedResults);

      // Return the response in the format expected by the client
      return NextResponse.json(createApiResponse(externalResponse, log.id, enhancedResults));

    } catch (error) {
      // Update the log with the error
      await updateLogWithError(log.id, error);

      // Return error response
      return NextResponse.json(
        createTaxInvoiceErrorResponse(error, log.id),
        { status: 500 }
      );
    }

  } catch (error) {
    if (process.env.NODE_ENV !== 'test') {
      console.error('Error processing tax invoice from URL:', error);
    }
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      {
        error: errorMessage,
        results: { success: false }
      },
      { status: 500 }
    );
  }
}
