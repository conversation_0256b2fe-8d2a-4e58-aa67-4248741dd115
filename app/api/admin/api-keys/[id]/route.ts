import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/db';

/**
 * Get a specific API key
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the API key
    const apiKey = await prisma.ApiKey.findUnique({
      where: {
        id: params.id,
      },
      select: {
        id: true,
        name: true,
        description: true,
        createdAt: true,
        updatedAt: true,
        lastUsedAt: true,
        isActive: true,
        createdBy: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!apiKey) {
      return NextResponse.json({ error: 'API key not found' }, { status: 404 });
    }

    return NextResponse.json({ apiKey });
  } catch (error) {
    console.error('Error getting API key:', error);
    return NextResponse.json(
      { error: 'Error getting API key' },
      { status: 500 }
    );
  }
}

/**
 * Update an API key (revoke/activate)
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { isActive } = body;

    // Validate request body
    if (isActive === undefined) {
      return NextResponse.json(
        { error: 'isActive is required' },
        { status: 400 }
      );
    }

    try {
      // Update the API key
      const apiKey = await prisma.ApiKey.update({
        where: {
          id: params.id,
        },
        data: {
          isActive,
          updatedAt: new Date(),
        },
      });

      return NextResponse.json({
        id: apiKey.id,
        name: apiKey.name,
        isActive: apiKey.isActive,
      });
    } catch (updateError) {
      // Handle the case where the API key doesn't exist
      if (updateError.code === 'P2025') {
        return NextResponse.json(
          { error: 'API key not found' },
          { status: 404 }
        );
      }
      throw updateError;
    }
  } catch (error) {
    console.error('Error updating API key:', error);
    return NextResponse.json(
      { error: 'Error updating API key' },
      { status: 500 }
    );
  }
}

/**
 * Delete an API key
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
      // Delete the API key
      await prisma.ApiKey.delete({
        where: {
          id: params.id,
        },
      });

      return NextResponse.json({ success: true });
    } catch (deleteError) {
      // Handle the case where the API key doesn't exist
      if (deleteError.code === 'P2025') {
        return NextResponse.json(
          { error: 'API key not found' },
          { status: 404 }
        );
      }
      throw deleteError;
    }
  } catch (error) {
    console.error('Error deleting API key:', error);
    return NextResponse.json(
      { error: 'Error deleting API key' },
      { status: 500 }
    );
  }
}
