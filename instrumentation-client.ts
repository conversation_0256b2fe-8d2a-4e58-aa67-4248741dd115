// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

Sentry.init({
  // Use the public DSN from environment variables for flexibility and security
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // Tag events with environment for filtering in Sentry web UI
  environment: process.env.NODE_ENV || "development",

  // Use a lower tracesSampleRate in production to reduce cost and noise
  tracesSampleRate: process.env.NODE_ENV === "production" ? 0.05 : 1,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,
});

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;