-- Add api_key_id field to the processing_logs table
ALTER TABLE "processing_logs" ADD COLUMN IF NOT EXISTS "api_key_id" UUID;

-- Add foreign key constraint
ALTER TABLE "processing_logs" ADD CONSTRAINT "processing_logs_api_key_id_fkey"
FOREIGN KEY ("api_key_id") REFERENCES "api_keys"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add index for api_key_id
CREATE INDEX IF NOT EXISTS "processing_log_api_key_id_idx" ON "processing_logs"("api_key_id");
