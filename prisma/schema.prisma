generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model ProcessingLog {
  id                 String    @id @default(uuid()) @map("id")
  requestType        String    @map("request_type")
  requestTimestamp   DateTime  @default(now()) @map("request_timestamp")
  fileName           String?   @map("file_name")
  fileSize           Int?      @map("file_size")
  processingStatus   String    @map("processing_status")
  errorMessage       String?   @map("error_message")
  processingId       String?   @map("processing_id")
  inputVendorName    String?   @map("input_vendor_name")
  ocrVendorName      String?   @map("ocr_vendor_name")
  inputDocumentNo    String?   @map("input_document_no")
  ocrDocumentNo      String?   @map("ocr_document_no")
  inputDocumentDate  DateTime? @map("input_document_date")
  ocrDocumentDate    DateTime? @map("ocr_document_date")
  inputInvoiceAmount Float?    @map("input_invoice_amount")
  ocrInvoiceAmount   Float?    @map("ocr_invoice_amount")
  inputVatAmount     Float?    @map("input_vat_amount")
  ocrVatAmount       Float?    @map("ocr_vat_amount")
  isColored          Boolean?  @map("is_colored")
  colorPages         Int[]     @default([]) @map("color_pages")
  totalPages         Int?      @map("total_pages")
  matchingResult     Json?     @map("matching_result")
  totalFields        Int?      @map("total_fields")
  matchedFields      Int?      @map("matched_fields")
  mismatchedFields   Int?      @map("mismatched_fields")
  notFoundFields     Int?      @map("not_found_fields")
  processedAt        DateTime? @map("processed_at")
  apiKeyId           String?   @map("api_key_id")
  apiKey             ApiKey?   @relation("UsedApiKey", fields: [apiKeyId], references: [id])

  @@index([requestType], map: "processing_log_request_type_idx")
  @@index([processingStatus], map: "processing_log_processing_status_idx")
  @@index([requestTimestamp], map: "processing_log_request_timestamp_idx")
  @@index([apiKeyId], map: "processing_log_api_key_id_idx")
  @@map("processing_logs")
}

model AdminUser {
  id           String   @id @default(uuid())
  name         String
  email        String   @unique
  passwordHash String
  role         String   @default("admin")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  apiKeys      ApiKey[] @relation("CreatedByUser")

  @@map("admin_users")
}

model ApiKey {
  id             String          @id @default(uuid())
  name           String
  keyHash        String          @unique @map("key_hash")
  description    String?
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")
  lastUsedAt     DateTime?       @map("last_used_at")
  isActive       Boolean         @default(true) @map("is_active")
  createdById    String          @map("created_by_id")
  createdBy      AdminUser       @relation("CreatedByUser", fields: [createdById], references: [id])
  processingLogs ProcessingLog[] @relation("UsedApiKey")

  @@index([keyHash])
  @@map("api_keys")
}
