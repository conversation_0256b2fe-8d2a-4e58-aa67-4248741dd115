import { describe, it, expect, vi, beforeEach } from 'vitest'
import { getPdfPageCount, getPdfPageCountFromUrl } from '@/lib/api/pdf-utils'

// Mock the pdfjs-dist module
vi.mock('pdfjs-dist/legacy/build/pdf.mjs', () => {
  const mockPdfDocument = {
    numPages: 7,
    destroy: vi.fn().mockResolvedValue(undefined)
  }

  const mockLoadingTask = {
    promise: Promise.resolve(mockPdfDocument)
  }

  return {
    getDocument: vi.fn().mockReturnValue(mockLoadingTask),
    GlobalWorkerOptions: {
      workerSrc: ''
    }
  }
})

// Mock the fs module
vi.mock('fs', () => ({
  existsSync: vi.fn().mockReturnValue(true),
  readFileSync: vi.fn().mockReturnValue(Buffer.from('mock pdf content'))
}))

// Mock the path module
vi.mock('path', () => ({
  join: vi.fn().mockReturnValue('/mock/path/to/worker.js'),
  dirname: vi.fn().mockReturnValue('/mock/path')
}))

// Mock the url module
vi.mock('url', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    fileURLToPath: vi.fn().mockReturnValue('/mock/path/to/current/file.js'),
    pathToFileURL: vi.fn().mockReturnValue({ href: 'file:///mock/path/to/worker.js' })
  }
})

describe('PDF Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getPdfPageCount', () => {
    it('should return the number of pages in a PDF buffer', async () => {
      const buffer = Buffer.from('mock pdf content')
      const pageCount = await getPdfPageCount(buffer)

      expect(pageCount).toBe(7)
    })

    it('should handle errors and return 0', async () => {
      // Mock getDocument to throw an error
      const getDocument = vi.spyOn(await import('pdfjs-dist/legacy/build/pdf.mjs'), 'getDocument')
      getDocument.mockImplementationOnce(() => {
        return {
          promise: Promise.reject(new Error('Failed to load PDF'))
        }
      })

      const buffer = Buffer.from('invalid pdf content')
      const pageCount = await getPdfPageCount(buffer)

      expect(pageCount).toBe(0)
    })
  })

  describe('getPdfPageCountFromUrl', () => {
    it('should return the number of pages in a PDF from a URL', async () => {
      const url = 'https://example.com/sample.pdf'
      const pageCount = await getPdfPageCountFromUrl(url)

      expect(pageCount).toBe(7)
    })

    it('should handle errors and return 0', async () => {
      // Mock getDocument to throw an error
      const getDocument = vi.spyOn(await import('pdfjs-dist/legacy/build/pdf.mjs'), 'getDocument')
      getDocument.mockImplementationOnce(() => {
        return {
          promise: Promise.reject(new Error('Failed to load PDF from URL'))
        }
      })

      const url = 'https://example.com/invalid.pdf'
      const pageCount = await getPdfPageCountFromUrl(url)

      expect(pageCount).toBe(0)
    })
  })
})
