import { describe, it, expect, vi, beforeEach } from 'vitest'
import { PrismaClient } from '@prisma/client'

// Mock PrismaClient
vi.mock('@prisma/client', () => {
  const mockPrismaClient = vi.fn()
  mockPrismaClient.prototype.constructor = mockPrismaClient
  mockPrismaClient.prototype.$connect = vi.fn()
  mockPrismaClient.prototype.$disconnect = vi.fn()

  return {
    PrismaClient: mockPrismaClient
  }
})

describe('Database Connection', () => {
  beforeEach(() => {
    vi.resetModules()
    vi.clearAllMocks()
  })

  it('should create a single PrismaClient instance', async () => {
    // Import the module to test
    const { prisma: prismaInstance1 } = await import('@/lib/db')

    // Import it again to verify it's the same instance
    const { prisma: prismaInstance2 } = await import('@/lib/db')

    // Verify that PrismaClient was instantiated
    expect(PrismaClient).toHaveBeenCalled()

    // Verify that both imports return the same instance
    expect(prismaInstance1).toBe(prismaInstance2)
  })

  it('should configure logging based on environment', async () => {
    // This test is simplified since we can't easily test the constructor arguments
    // with the current mocking approach

    // Save original NODE_ENV
    const originalNodeEnv = process.env.NODE_ENV

    // Test development environment
    process.env.NODE_ENV = 'development'
    vi.resetModules()

    // Just verify that the module can be imported without errors
    const devModule = await import('@/lib/db')
    expect(devModule).toBeDefined()

    // Test production environment
    process.env.NODE_ENV = 'production'
    vi.resetModules()

    // Just verify that the module can be imported without errors
    const prodModule = await import('@/lib/db')
    expect(prodModule).toBeDefined()

    // Restore original NODE_ENV
    process.env.NODE_ENV = originalNodeEnv
  })
})
