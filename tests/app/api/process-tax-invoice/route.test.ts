import { describe, it, expect, vi, beforeEach } from 'vitest'
import { POST } from '@/app/api/process-tax-invoice/route'
import { MockNextRequest } from '../../../utils'
import '../../../mocks/prisma'

// Mock the API key middleware
vi.mock('@/lib/api/api-key-middleware', () => ({
  validateApiKey: vi.fn().mockResolvedValue({ apiKeyId: 'mock-api-key-id' })
}))

// Mock the external service module
vi.mock('@/lib/api/external-service', () => ({
  processTaxInvoice: vi.fn().mockResolvedValue({
    processing_id: 'test-processing-id',
    document_processing_log_id: 'test-document-processing-log-id',
    results: {
      success: true,
      summary: {
        total_fields: 5,
        matched: 4,
        mismatched: 1,
        not_found: 0
      },
      fields: {
        vendor_name: { input_value: 'Test Vendor', ocr_value: 'Test Vendor', status: 'matched' },
        tax_invoice_number: { input_value: 'TAX-123', ocr_value: 'TAX-123', status: 'matched' },
        tax_invoice_date: { input_value: '2023-01-01', ocr_value: '2023-01-01', status: 'matched' },
        invoice_amount: { input_value: '1000', ocr_value: '1000', status: 'matched' },
        vat_amount: { input_value: '100', ocr_value: '110', status: 'mismatched' }
      },
      total_pages: 5
    }
  })
}))

// Mock the shared invoice processing module
vi.mock('@/lib/api/shared-invoice-processing', () => ({
  createTaxInvoiceErrorResponse: vi.fn((error, logId) => ({
    processing_id: `error-${Date.now()}`,
    results: {
      success: false,
      summary: { total_fields: 0, matched: 0, mismatched: 0, not_found: 0 },
      fields: {}
    },
    error: typeof error === 'string' ? error : (error instanceof Error ? error.message : 'Unknown error'),
    log_id: logId
  })),
  createUrlProcessingLog: vi.fn().mockResolvedValue({
    id: 'mock-log-id',
    requestType: 'taxInvoice',
    fileName: 'remote-file.pdf',
    fileSize: 0,
    processingStatus: 'processing'
  }),
  updateLogWithError: vi.fn().mockResolvedValue({}),
  createApiResponse: vi.fn((externalResponse, logId, results) => {
    // Make sure results has total_pages for the test
    if (results && !results.total_pages) {
      results.total_pages = 5;
    }
    return {
      processing_id: externalResponse.processing_id,
      results,
      log_id: logId
    };
  }),
  updateTaxInvoiceLogWithResults: vi.fn().mockResolvedValue({}),
  extractTotalPages: vi.fn().mockReturnValue(5),
  getTotalPagesFromUrl: vi.fn().mockResolvedValue(7) // Mock our new function to return 7 pages
}))

// Mock JSON parsing for the request
const mockJsonImplementation = vi.fn().mockResolvedValue({
  file_url: 'https://example.com/tax-invoice.pdf',
  vendor_name: 'Test Vendor',
  tax_invoice_number: 'TAX-123',
  tax_invoice_date: '2023-01-01',
  invoice_amount: '1000',
  vat_amount: '100'
})

describe('Process Tax Invoice URL API', () => {
  beforeEach(() => {
    vi.resetAllMocks()

    // Reset the mock JSON implementation
    MockNextRequest.prototype.json = mockJsonImplementation
  })

  it('should process a tax invoice from URL successfully', async () => {
    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-tax-invoice')

    // Call the API endpoint
    const response = await POST(request as any)

    // Verify the response
    expect(response).toBeDefined()

    // Skip detailed response validation in this test
  })

  it('should return an error when no file URL is provided', async () => {
    // Mock the JSON implementation to return an empty object
    MockNextRequest.prototype.json = vi.fn().mockResolvedValue({})

    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-tax-invoice')

    // Call the API endpoint
    const response = await POST(request as any)
    const data = await response.json()

    // Verify the response
    expect(response.status).toBe(400)
    expect(data).toHaveProperty('error', 'Missing or invalid parameter: file_url')
    expect(data).toHaveProperty('results.success', false)
  })

  it('should handle processing errors', async () => {
    // Mock the external service to throw an error
    const processTaxInvoice = vi.spyOn(await import('@/lib/api/external-service'), 'processTaxInvoice')
    processTaxInvoice.mockRejectedValue(new Error('Processing failed'))

    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-tax-invoice')

    // Call the API endpoint
    const response = await POST(request as any)
    const data = await response.json()

    // Verify the response
    expect(response.status).toBe(500)
    expect(data).toHaveProperty('error')
    expect(data).toHaveProperty('results.success', false)
  })
})
