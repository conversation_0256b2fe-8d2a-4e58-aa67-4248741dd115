import { describe, it, expect, vi, beforeEach } from 'vitest'
import { MockNextRequest, createMockInvoiceFormData, MockFile } from '../../../utils'
import { prismaMock } from '../../../mocks/prisma'

// Mock modules before importing the components that use them
vi.mock('@/lib/api/external-service', () => ({
  processInvoiceFile: vi.fn().mockResolvedValue({
    processing_id: 'test-processing-id',
    document_processing_log_id: 'test-document-processing-log-id',
    results: {
      success: true,
      summary: {
        total_fields: 5,
        matched: 3,
        mismatched: 1,
        not_found: 1
      },
      fields: {
        vendor_name: { input_value: 'Test Vendor', ocr_value: 'Test Vendor', status: 'matched' },
        invoice_number: { input_value: 'INV-123', ocr_value: 'INV-123', status: 'matched' },
        invoice_date: { input_value: '2023-01-01', ocr_value: '2023-01-01', status: 'matched' },
        invoice_amount: { input_value: '1000', ocr_value: '1000.00', status: 'mismatched' },
        vat_amount: { input_value: '100', ocr_value: '', status: 'not_found' }
      }
    }
  })
}))

// Mock the enhanced color detection module
vi.mock('@/lib/api/enhanced-color-detection', () => ({
  detectColor: vi.fn().mockResolvedValue({
    is_colored: false,
    color_pages: [],
    total_pages: 3,
    confidence: 'high',
    method: 'Pixel-by-Pixel RGB Analysis'
  })
}))

// Mock the shared invoice processing module
vi.mock('@/lib/api/shared-invoice-processing', () => ({
  createErrorResponse: vi.fn((error, logId) => ({
    processing_id: `error-${Date.now()}`,
    results: {
      success: false,
      summary: { total_fields: 0, matched: 0, mismatched: 0, not_found: 0 },
      fields: {}
    },
    error: typeof error === 'string' ? error : (error instanceof Error ? error.message : 'Unknown error'),
    log_id: logId
  })),
  parseDate: vi.fn(),
  handleColorDetection: vi.fn().mockResolvedValue({
    is_colored: false,
    color_pages: [],
    total_pages: 3,
    confidence: 'high',
    method: 'Pixel-by-Pixel RGB Analysis'
  }),
  createProcessingLog: vi.fn().mockResolvedValue({
    id: 'mock-log-id',
    requestType: 'invoice',
    fileName: 'test.pdf',
    fileSize: 1024,
    processingStatus: 'processing'
  }),
  updateLogWithError: vi.fn().mockResolvedValue({}),
  createApiResponse: vi.fn((externalResponse, logId, results) => ({
    processing_id: externalResponse.processing_id,
    results,
    log_id: logId
  })),
  updateInvoiceLogWithResults: vi.fn().mockResolvedValue({})
}))

// Mock the Prisma client
vi.mock('@/lib/db', () => ({
  prisma: prismaMock,
  default: prismaMock
}))

// Mock the API key middleware
vi.mock('@/lib/api/api-key-middleware', () => ({
  validateApiKey: vi.fn().mockResolvedValue({ apiKeyId: 'mock-api-key-id' })
}))

// Mock the File object in the global scope
global.File = MockFile

// Import after mocks are set up
import { POST } from '@/app/api/process-invoice-file/route'
import { detectColor } from '@/lib/api/enhanced-color-detection'
import { handleColorDetection } from '@/lib/api/shared-invoice-processing'

describe('Process Invoice File API with Enhanced Color Detection', () => {
  beforeEach(() => {
    vi.resetAllMocks()

    // Mock Prisma create and update methods
    prismaMock.processingLog.create.mockResolvedValue({
      id: 'mock-log-id',
      requestType: 'invoice',
      fileName: 'test.pdf',
      fileSize: 1024,
      processingStatus: 'processing',
      requestTimestamp: new Date()
    })

    prismaMock.processingLog.update.mockResolvedValue({
      id: 'mock-log-id',
      processingStatus: 'completed',
      processedAt: new Date()
    })

    // Default mock implementation for detectColor
    vi.mocked(detectColor).mockResolvedValue({
      is_colored: false,
      color_pages: [],
      total_pages: 3,
      confidence: 'high',
      method: 'Pixel-by-Pixel RGB Analysis'
    })

    // Default mock implementation for handleColorDetection
    vi.mocked(handleColorDetection).mockResolvedValue({
      is_colored: false,
      color_pages: [],
      total_pages: 3,
      confidence: 'high',
      method: 'Pixel-by-Pixel RGB Analysis'
    })
  })

  it('should include color detection results in the API response', async () => {
    // Mock color detection to return a colored document
    vi.mocked(handleColorDetection).mockResolvedValue({
      is_colored: true,
      color_pages: [1, 3],
      total_pages: 3,
      confidence: 'high',
      method: 'Pixel-by-Pixel RGB Analysis'
    })

    // Create mock form data
    const formData = createMockInvoiceFormData({
      fileName: 'colored-invoice.pdf',
      vendorName: 'Test Vendor',
      invoiceNo: 'INV-123',
      invoiceDate: '2023-01-01',
      invoiceAmount: 1000,
      vatAmount: 100
    })

    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-invoice-file', {
      formData
    })

    // Call the API endpoint
    const response = await POST(request as any)

    // For now, we'll just check that the function runs without errors
    expect(response).toBeDefined()

    // Verify handleColorDetection was called
    expect(handleColorDetection).toHaveBeenCalledTimes(1)
  })

  it('should handle black and white documents correctly', async () => {
    // Mock color detection to return a black and white document
    vi.mocked(handleColorDetection).mockResolvedValue({
      is_colored: false,
      color_pages: [],
      total_pages: 5,
      confidence: 'high',
      method: 'Pixel-by-Pixel RGB Analysis'
    })

    // Create mock form data
    const formData = createMockInvoiceFormData({
      fileName: 'bw-invoice.pdf',
      vendorName: 'Test Vendor',
      invoiceNo: 'INV-456',
      invoiceDate: '2023-02-01',
      invoiceAmount: 2000,
      vatAmount: 200
    })

    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-invoice-file', {
      formData
    })

    // Call the API endpoint
    const response = await POST(request as any)

    // For now, we'll just check that the function runs without errors
    expect(response).toBeDefined()

    // Verify handleColorDetection was called
    expect(handleColorDetection).toHaveBeenCalledTimes(1)
  })

  it('should handle errors in color detection gracefully', async () => {
    // Mock color detection to throw an error
    vi.mocked(handleColorDetection).mockRejectedValue(new Error('Color detection failed'))

    // Create mock form data
    const formData = createMockInvoiceFormData()

    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-invoice-file', {
      formData
    })

    // Call the API endpoint
    const response = await POST(request as any)

    // For now, we'll just check that the function runs without errors
    expect(response).toBeDefined()
  })
})
