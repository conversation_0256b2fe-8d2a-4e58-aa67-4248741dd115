import { describe, it, expect, vi, beforeEach } from 'vitest'
import { MockNextRequest } from '../../../utils'
import { prismaMock } from '../../../mocks/prisma'

// Mock modules before importing the components that use them
vi.mock('@/lib/api/external-service', () => ({
  processInvoice: vi.fn().mockResolvedValue({
    processing_id: 'test-processing-id',
    document_processing_log_id: 'test-document-processing-log-id',
    results: {
      success: true,
      summary: {
        total_fields: 5,
        matched: 3,
        mismatched: 1,
        not_found: 1
      },
      fields: {
        vendor_name: { input_value: 'Test Vendor', ocr_value: 'Test Vendor', status: 'matched' },
        invoice_number: { input_value: 'INV-123', ocr_value: 'INV-123', status: 'matched' },
        invoice_date: { input_value: '2023-01-01', ocr_value: '2023-01-01', status: 'matched' },
        invoice_amount: { input_value: '1000', ocr_value: '1000.00', status: 'mismatched' },
        vat_amount: { input_value: '100', ocr_value: '', status: 'not_found' }
      }
    }
  })
}))

// Mock the shared invoice processing module
vi.mock('@/lib/api/shared-invoice-processing', () => ({
  createErrorResponse: vi.fn((error, logId) => ({
    processing_id: `error-${Date.now()}`,
    results: {
      success: false,
      summary: { total_fields: 0, matched: 0, mismatched: 0, not_found: 0 },
      fields: {}
    },
    error: typeof error === 'string' ? error : (error instanceof Error ? error.message : 'Unknown error'),
    log_id: logId
  })),
  createUrlProcessingLog: vi.fn().mockResolvedValue({
    id: 'mock-log-id',
    requestType: 'invoice',
    fileName: 'remote-file.pdf',
    fileSize: 0,
    processingStatus: 'processing'
  }),
  updateLogWithError: vi.fn().mockResolvedValue({}),
  createApiResponse: vi.fn((externalResponse, logId, results) => ({
    processing_id: externalResponse.processing_id,
    results,
    log_id: logId
  })),
  updateInvoiceLogWithResults: vi.fn().mockResolvedValue({})
}))

// Mock the Prisma client
vi.mock('@/lib/db', () => ({
  prisma: prismaMock,
  default: prismaMock
}))

// Mock the API key middleware
vi.mock('@/lib/api/api-key-middleware', () => ({
  validateApiKey: vi.fn().mockResolvedValue({ apiKeyId: 'mock-api-key-id' })
}))

// Mock JSON parsing for the request
const mockJsonImplementation = vi.fn().mockResolvedValue({
  file_url: 'https://example.com/invoice.pdf',
  vendor_name: 'Test Vendor',
  invoice_number: 'INV-123',
  invoice_date: '2023-01-01',
  invoice_amount: '1000',
  vat_amount: '100'
})

// Import after mocks are set up
import { POST } from '@/app/api/process-invoice/route'

describe('Process Invoice URL API', () => {
  beforeEach(() => {
    vi.resetAllMocks()

    // Mock Prisma update method
    prismaMock.processingLog.update.mockResolvedValue({
      id: 'mock-log-id',
      processingStatus: 'completed',
      processedAt: new Date()
    })

    // Reset the mock JSON implementation
    MockNextRequest.prototype.json = mockJsonImplementation
  })

  it('should process an invoice from URL successfully', async () => {
    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-invoice')

    // Call the API endpoint
    const response = await POST(request as any)

    // For now, we'll just check that the function runs without errors
    expect(response).toBeDefined()
  })

  it('should return an error when no file URL is provided', async () => {
    // Mock the JSON implementation to return an empty object
    MockNextRequest.prototype.json = vi.fn().mockResolvedValue({})

    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-invoice')

    // Call the API endpoint
    const response = await POST(request as any)

    // For now, we'll just check that the function runs without errors
    expect(response).toBeDefined()
    expect(response.status).toBe(400)
  })

  it('should handle processing errors', async () => {
    // Mock the external service to throw an error
    const processInvoice = vi.spyOn(await import('@/lib/api/external-service'), 'processInvoice')
    processInvoice.mockRejectedValue(new Error('Processing failed'))

    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-invoice')

    // Call the API endpoint
    const response = await POST(request as any)

    // For now, we'll just check that the function runs without errors
    expect(response).toBeDefined()
  })
})