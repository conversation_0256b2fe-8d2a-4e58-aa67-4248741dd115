import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the generateApiKey function in the route file
vi.mock('@/app/api/admin/api-keys/route', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    GET: actual.GET,
    POST: actual.POST,
    // Override the internal generateApiKey function
    default: {
      generateApiKey: vi.fn().mockReturnValue({
        fullKey: 'ki_12345678.test-api-key-secret',
        hashedKey: 'hashed-key'
      })
    }
  };
});

// Mock next-auth
vi.mock('next-auth', async () => {
  return {
    default: function() { return { GET: () => {}, POST: () => {} }; },
    getServerSession: vi.fn(),
  }
});

// Mock next-auth/next
vi.mock('next-auth/next', () => ({
  default: function() { return { GET: () => {}, POST: () => {} }; },
  getServerSession: vi.fn(),
}));

// Mock authOptions
vi.mock('@/app/api/auth/[...nextauth]/route', () => ({
  authOptions: {},
}));

// Mock Prisma client
vi.mock('@/lib/db', () => ({
  default: {
    ApiKey: {
      findMany: vi.fn(),
      create: vi.fn(),
    },
  },
}));

// Import after mocks
import { GET, POST, generateApiKey } from '@/app/api/admin/api-keys/route';
import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import prisma from '@/lib/db';

describe('API Keys API Routes', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('GET /api/admin/api-keys', () => {
    it('should return 401 if user is not authenticated', async () => {
      // Mock getServerSession to return null (not authenticated)
      vi.mocked(getServerSession).mockResolvedValue(null);

      // Create a mock request
      const req = new NextRequest('https://example.com/api/admin/api-keys');

      // Call the API endpoint
      const response = await GET(req);

      // Verify the response
      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data).toHaveProperty('error', 'Unauthorized');
    });

    it('should return API keys if user is authenticated', async () => {
      // Mock getServerSession to return a session (authenticated)
      vi.mocked(getServerSession).mockResolvedValue({
        user: {
          id: 'user-id',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'admin',
        },
        expires: '2023-01-01',
      });

      // Mock the findMany method to return API keys
      const mockApiKeys = [
        {
          id: 'key-1',
          name: 'API Key 1',
          description: 'Test description',
          createdAt: new Date(),
          updatedAt: new Date(),
          lastUsedAt: null,
          isActive: true,
          createdBy: {
            name: 'Test User',
            email: '<EMAIL>',
          },
        },
      ];
      vi.mocked(prisma.ApiKey.findMany).mockResolvedValue(mockApiKeys);

      // Create a mock request
      const req = new NextRequest('https://example.com/api/admin/api-keys');

      // Call the API endpoint
      const response = await GET(req);

      // Verify the response
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data).toHaveProperty('apiKeys');

      // Check that the API keys have the expected properties without comparing date objects directly
      expect(data.apiKeys.length).toBe(mockApiKeys.length);
      expect(data.apiKeys[0].id).toBe(mockApiKeys[0].id);
      expect(data.apiKeys[0].name).toBe(mockApiKeys[0].name);
      expect(data.apiKeys[0].description).toBe(mockApiKeys[0].description);
      expect(data.apiKeys[0].isActive).toBe(mockApiKeys[0].isActive);
    });
  });

  describe('POST /api/admin/api-keys', () => {
    it('should return 401 if user is not authenticated', async () => {
      // Mock getServerSession to return null (not authenticated)
      vi.mocked(getServerSession).mockResolvedValue(null);

      // Create a mock request
      const req = new NextRequest('https://example.com/api/admin/api-keys', {
        method: 'POST',
        body: JSON.stringify({
          name: 'New API Key',
          description: 'Test description',
        }),
      });

      // Call the API endpoint
      const response = await POST(req);

      // Verify the response
      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data).toHaveProperty('error', 'Unauthorized');
    });

    it('should return 400 if name is not provided', async () => {
      // Mock getServerSession to return a session (authenticated)
      vi.mocked(getServerSession).mockResolvedValue({
        user: {
          id: 'user-id',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'admin',
        },
        expires: '2023-01-01',
      });

      // Create a mock request without a name
      const req = new NextRequest('https://example.com/api/admin/api-keys', {
        method: 'POST',
        body: JSON.stringify({
          description: 'Test description',
        }),
      });

      // Call the API endpoint
      const response = await POST(req);

      // Verify the response
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty('error', 'Name is required');
    });

    it('should create a new API key if user is authenticated and name is provided', async () => {
      // Mock getServerSession to return a session (authenticated)
      vi.mocked(getServerSession).mockResolvedValue({
        user: {
          id: 'user-id',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'admin',
        },
        expires: '2023-01-01',
      });

      // Mock the create method to return a new API key
      const mockApiKey = {
        id: 'key-1',
        name: 'New API Key',
        description: 'Test description',
        keyHash: 'hashed-key', // Now using keyHash instead of key
        createdAt: new Date(),
        updatedAt: new Date(),
        lastUsedAt: null,
        isActive: true,
        createdById: 'user-id',
      };
      vi.mocked(prisma.ApiKey.create).mockResolvedValue(mockApiKey);

      // Create a mock request with a name
      const req = new NextRequest('https://example.com/api/admin/api-keys', {
        method: 'POST',
        body: JSON.stringify({
          name: 'New API Key',
          description: 'Test description',
        }),
      });

      // Call the API endpoint
      const response = await POST(req);

      // Verify the response
      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data).toHaveProperty('id', 'key-1');
      expect(data).toHaveProperty('name', 'New API Key');
      expect(data).toHaveProperty('description', 'Test description');
      // Just check that the key exists and is a string, since the actual value is generated at runtime
      expect(data).toHaveProperty('key');
      expect(typeof data.key).toBe('string');

      // Verify that the API key was created with the correct data
      expect(prisma.ApiKey.create).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'New API Key',
          description: 'Test description',
          createdById: 'user-id',
        }),
      }));
    });
  });
});
