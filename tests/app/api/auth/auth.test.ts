// Mock imports must be at the top
import { vi } from 'vitest';
import { prismaMock } from '@/tests/mocks/prisma';

// Mock Prisma client
vi.mock('@/lib/db', () => ({
  __esModule: true,
  default: prismaMock,
  prisma: prismaMock
}));

// Mock bcryptjs
vi.mock('bcryptjs', () => ({
  compare: vi.fn().mockResolvedValue(true)
}));

// Regular imports
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

describe('NextAuth Configuration', () => {
  const mockUser = {
    id: 'user-123',
    name: 'Test User',
    email: '<EMAIL>',
    passwordHash: 'hashed-password',
    role: 'admin',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    vi.resetAllMocks();
    prismaMock.adminUser.findUnique.mockResolvedValue(mockUser);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should have the correct providers and configuration', () => {
    expect(authOptions.providers).toHaveLength(1);
    expect(authOptions.providers[0].id).toBe('credentials');
    expect(authOptions.session?.strategy).toBe('jwt');
    expect(authOptions.pages?.signIn).toBe('/admin/login');
  });

  it('should transform JWT token correctly', async () => {
    const { callbacks } = authOptions;
    const jwtCallback = callbacks?.jwt;

    if (!jwtCallback) {
      throw new Error('JWT callback not found');
    }

    const token = { name: 'Test User', email: '<EMAIL>', id: '', role: '' };
    const user = {
      id: 'user-123',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'admin'
    };

    const result = await jwtCallback({
      token,
      user: user as any,
      account: null,
      profile: undefined,
      trigger: 'signIn'
    });

    expect(result).toEqual({
      name: 'Test User',
      email: '<EMAIL>',
      id: 'user-123',
      role: 'admin'
    });
  });

  it('should transform session correctly', async () => {
    const { callbacks } = authOptions;
    const sessionCallback = callbacks?.session;

    if (!sessionCallback) {
      throw new Error('Session callback not found');
    }

    const session = {
      user: { name: 'Test User', email: '<EMAIL>', id: '', role: '' },
      expires: new Date().toISOString()
    };
    const token = {
      name: 'Test User',
      email: '<EMAIL>',
      id: 'user-123',
      role: 'admin'
    };

    const result = await sessionCallback({
      session: session as any,
      token,
      user: null as any,
      newSession: null as any,
      trigger: 'update'
    });

    expect(result.user).toEqual({
      name: 'Test User',
      email: '<EMAIL>',
      id: 'user-123',
      role: 'admin'
    });
  });
});