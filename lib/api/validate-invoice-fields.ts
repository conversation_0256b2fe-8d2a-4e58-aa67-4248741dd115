// Utility to validate required invoice fields by type
// Returns an error message if invalid, otherwise null
export function validateInvoiceFields(
  data: Record<string, unknown>,
  requiredFields: { key: string; name: string; type: 'string' | 'number' | 'date' }[]
): string | null {
  for (const field of requiredFields) {
    const value = data[field.key];
    if (field.type === 'string') {
      if (
        typeof value !== 'string' ||
        !value ||
        (typeof value === 'string' && value.trim() === '')
      ) {
        return `Missing or invalid parameter: ${field.name}`;
      }
    } else if (field.type === 'number') {
      if (
        typeof value !== 'number' ||
        isNaN(value)
      ) {
        return `Missing or invalid parameter: ${field.name}`;
      }
    } else if (field.type === 'date') {
      if (typeof value !== 'string' || !/^\d{4}-\d{2}-\d{2}$/.test(value)) {
        return `Missing or invalid parameter (date must be YYYY-MM-DD): ${field.name}`;
      }
    }
  }
  return null;
}
