/**
 * PDF utility functions for invoice processing
 */

import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist/legacy/build/pdf.mjs';
import { fileURLToPath, pathToFileURL } from 'url';
import * as path from 'path';
import * as fs from 'fs';

// Set up the worker source for PDF.js
try {
  // Try to find the worker script in various locations
  let workerSrcPath = '';

  // Option 1: Check in node_modules relative to current file
  const currentFilePath = __filename;
  const currentDir = path.dirname(currentFilePath);
  const possiblePaths = [
    path.join(currentDir, '../node_modules/pdfjs-dist/legacy/build/pdf.worker.mjs'),
    path.join(currentDir, '../../node_modules/pdfjs-dist/legacy/build/pdf.worker.mjs'),
    path.join(process.cwd(), 'node_modules/pdfjs-dist/legacy/build/pdf.worker.mjs')
  ];

  for (const possiblePath of possiblePaths) {
    if (fs.existsSync(possiblePath)) {
      workerSrcPath = possiblePath;
      break;
    }
  }

  if (workerSrcPath) {
    GlobalWorkerOptions.workerSrc = pathToFileURL(workerSrcPath).href;
  } else {
    console.warn('PDF.js worker script not found. PDF page counting may not work correctly.');
  }
} catch (error) {
  console.warn('Failed to set PDF.js worker source:', error);
}

/**
 * Counts the number of pages in a PDF from a buffer
 *
 * @param pdfBuffer The PDF file as a buffer
 * @returns Promise resolving to the number of pages in the PDF
 */
export async function getPdfPageCount(pdfBuffer: Buffer): Promise<number> {
  let pdfDocument = null;
  let loadingTask;

  try {
    // Configure the loading task
    const loadingTaskConfig = {
      data: new Uint8Array(pdfBuffer),
      // Disable unnecessary features for faster loading
      disableAutoFetch: true,
      disableStream: true,
      disableRange: true
    };

    // Load the PDF document
    loadingTask = getDocument(loadingTaskConfig);
    pdfDocument = await loadingTask.promise;

    // Return the number of pages
    return pdfDocument.numPages;
  } catch (error) {
    console.error('Error while counting PDF pages:', error);
    // Return 0 if we couldn't determine the page count
    return 0;
  } finally {
    // Clean up resources
    if (pdfDocument && typeof pdfDocument.destroy === 'function') {
      try {
        await pdfDocument.destroy();
      } catch (error) {
        console.error('Error destroying PDF document:', error);
      }
    }
  }
}

/**
 * Counts the number of pages in a PDF from a URL
 *
 * @param url The URL of the PDF file
 * @returns Promise resolving to the number of pages in the PDF
 */
export async function getPdfPageCountFromUrl(url: string): Promise<number> {
  let pdfDocument = null;
  let loadingTask;

  try {
    // Configure the loading task
    const loadingTaskConfig = {
      url: url,
      // Disable unnecessary features for faster loading
      disableAutoFetch: true,
      disableStream: true,
      disableRange: true
    };

    // Load the PDF document
    loadingTask = getDocument(loadingTaskConfig);
    pdfDocument = await loadingTask.promise;

    // Return the number of pages
    return pdfDocument.numPages;
  } catch (error) {
    console.error('Error while counting PDF pages from URL:', error);
    // Return 0 if we couldn't determine the page count
    return 0;
  } finally {
    // Clean up resources
    if (pdfDocument && typeof pdfDocument.destroy === 'function') {
      try {
        await pdfDocument.destroy();
      } catch (error) {
        console.error('Error destroying PDF document:', error);
      }
    }
  }
}
