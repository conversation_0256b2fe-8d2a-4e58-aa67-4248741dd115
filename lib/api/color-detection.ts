import * as pdfjsLib from 'pdfjs-dist';
import path from 'path';

// Try to set up the PDF.js worker
try {
  pdfjsLib.GlobalWorkerOptions.workerSrc = path.join(process.cwd(), 'node_modules/pdfjs-dist/build/pdf.worker.js');
} catch (error) {
  console.warn('Failed to set PDF.js worker source:', error);
}

// Configure standard font data URL
let STANDARD_FONT_DATA_URL: string;
try {
  STANDARD_FONT_DATA_URL = path.join(process.cwd(), 'node_modules/pdfjs-dist/standard_fonts/');
} catch (error) {
  console.warn('Failed to set standard font data URL:', error);
  STANDARD_FONT_DATA_URL = '';
}

// Define the color detection result interface
export interface ColorDetectionResult {
  is_colored: boolean;
  color_pages: number[];
  total_pages: number;
}

// Import the fallback implementation
import { detectColor as detectColorFallback } from './color-detection-fallback';

/**
 * Checks if a pixel is considered black and white
 *
 * @param r - Red component (0-255)
 * @param g - Green component (0-255)
 * @param b - Blue component (0-255)
 * @param threshold - Threshold percentage (0-100)
 * @returns True if the pixel is black and white, false otherwise
 */
function isBlackAndWhitePixel(r: number, g: number, b: number, threshold: number): boolean {
  // Calculate the average of the RGB components
  const avg = (r + g + b) / 3;

  // If the average is 0 (black) or 255 (white), it's definitely black and white
  if (avg === 0 || avg === 255) {
    return true;
  }

  // Calculate the maximum allowed difference based on the threshold percentage
  const maxDiff = (avg * threshold) / 100;

  // Check if the difference between each component and the average is within the threshold
  return (
    Math.abs(r - avg) <= maxDiff &&
    Math.abs(g - avg) <= maxDiff &&
    Math.abs(b - avg) <= maxDiff
  );
}

/**
 * Analyzes the pixel data of a canvas to determine if it contains color
 *
 * @param pixelData - The pixel data array
 * @param options - Analysis options
 * @returns Object containing analysis results
 */
function analyzePixelData(pixelData: Uint8ClampedArray, options: { threshold?: number } = {}): { hasColor: boolean } {
  const { threshold = 1.0 } = options;

  let totalPixels = 0;
  let colorPixels = 0;

  // Iterate through the pixel data (RGBA format, 4 bytes per pixel)
  for (let i = 0; i < pixelData.length; i += 4) {
    const r = pixelData[i];
    const g = pixelData[i + 1];
    const b = pixelData[i + 2];
    const a = pixelData[i + 3];

    // Skip transparent pixels
    if (a === 0) {
      continue;
    }

    totalPixels++;

    if (!isBlackAndWhitePixel(r, g, b, threshold)) {
      colorPixels++;
    }
  }

  // If less than 0.5% of pixels are colored, consider it black and white
  const colorPercentage = totalPixels > 0 ? (colorPixels / totalPixels) * 100 : 0;
  return {
    hasColor: colorPercentage >= 0.5
  };
}

/**
 * Creates a canvas element for rendering
 *
 * @returns Canvas and context objects
 */
function createCanvas(width: number, height: number): { canvas: any, context: any } {
  let canvas, context;

  if (typeof document !== 'undefined') {
    // Browser environment
    canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    context = canvas.getContext('2d');
  } else {
    // Node.js environment
    try {
      // Try to use node-canvas if available
      // Using dynamic import to avoid dependency issues
      const canvasModule = require('canvas');
      canvas = canvasModule.createCanvas(width, height);
      context = canvas.getContext('2d');
    } catch (error) {
      // If node-canvas is not available, use a mock canvas
      console.warn('Canvas library not available. Using mock canvas. PDF color detection will not be accurate.');
      canvas = {
        width,
        height
      };
      context = {
        getImageData: () => ({ data: new Uint8ClampedArray(width * height * 4) })
      };
    }
  }

  return { canvas, context };
}

/**
 * Renders a PDF page to a canvas and analyzes its pixel data
 *
 * @param page - The PDF page to analyze
 * @param options - Options for the analysis
 * @returns Promise resolving to analysis results
 */
async function analyzePage(page: pdfjsLib.PDFPageProxy, options: { scale?: number, threshold?: number } = {}): Promise<{ hasColor: boolean }> {
  const { scale = 0.5, threshold = 1.0 } = options;

  try {
    // Get the viewport of the page at the specified scale
    const viewport = page.getViewport({ scale });

    // Create a canvas with the dimensions of the viewport
    const { canvas, context } = createCanvas(viewport.width, viewport.height);

    // Render the page to the canvas
    await page.render({
      canvasContext: context,
      viewport: viewport
    }).promise;

    // Get the pixel data from the canvas
    const pixelData = context.getImageData(0, 0, canvas.width, canvas.height).data;

    // Analyze the pixel data
    return analyzePixelData(pixelData, { threshold });
  } catch (error) {
    console.error('Error rendering PDF page:', error);
    // Return a default result if rendering fails
    return { hasColor: false };
  }
}

/**
 * Detects if a PDF invoice is colored and which pages contain color
 *
 * @param pdfBuffer The PDF file as a buffer
 * @returns Object containing color detection results
 */
export async function detectColor(pdfBuffer: Buffer): Promise<ColorDetectionResult> {
  // First try the canvas-based implementation
  try {
    // Check if we're in a Node.js environment
    const isNode = typeof window === 'undefined';

    // Check if canvas is available
    let canvasAvailable = false;
    try {
      if (isNode) {
        // Try to require canvas
        require('canvas');
        canvasAvailable = true;
      } else {
        // In browser, canvas is always available
        canvasAvailable = true;
      }
    } catch (error) {
      console.warn('Canvas library not available, will use fallback implementation');
      canvasAvailable = false;
    }

    // If canvas is not available, use the fallback implementation
    if (!canvasAvailable) {
      return detectColorFallback(pdfBuffer);
    }

    // Configure PDF.js options
    const options: any = {
      data: pdfBuffer,
      disableFontFace: true,
      ignoreErrors: true,
    };

    // Only set standardFontDataUrl in Node.js environment
    if (isNode && STANDARD_FONT_DATA_URL) {
      options.standardFontDataUrl = STANDARD_FONT_DATA_URL;
    }

    // Load the PDF document
    const loadingTask = pdfjsLib.getDocument(options);

    const pdf = await loadingTask.promise;
    const numPages = pdf.numPages;
    const colorPages: number[] = [];

    // Analyze each page
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const result = await analyzePage(page, {
          scale: 0.5,
          threshold: 1.0
        });

        if (result.hasColor) {
          colorPages.push(pageNum);
        }
      } catch (pageError) {
        console.error(`Error analyzing page ${pageNum}:`, pageError);
        // Continue with the next page
      }
    }

    return {
      is_colored: colorPages.length > 0,
      color_pages: colorPages,
      total_pages: numPages
    };
  } catch (error) {
    console.error('Error in canvas-based color detection, trying fallback:', error);

    // If the canvas-based implementation fails, try the fallback
    try {
      return await detectColorFallback(pdfBuffer);
    } catch (fallbackError) {
      console.error('Fallback color detection also failed:', fallbackError);
      // Return a default result if both implementations fail
      return {
        is_colored: false,
        color_pages: [],
        total_pages: 0
      };
    }
  }
}
