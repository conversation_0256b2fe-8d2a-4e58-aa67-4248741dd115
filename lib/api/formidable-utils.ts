import { NextRequest } from 'next/server';
import { promises as fs } from 'fs';

export interface ParsedFormData {
  fields: Record<string, string>;
  fileBuffer: Buffer;
  fileName: string;
  fileSize: number;
}

/**
 * Parse multipart form data with Formidable-like validation and size limits
 *
 * Note: This implementation uses Next.js built-in FormData parsing instead of
 * the actual Formidable library due to compatibility issues with Next.js 13+ App Router.
 * However, it provides the same validation features that Formidable would offer:
 * - File size limits (50MB)
 * - File type validation (PDF only)
 * - Empty file rejection
 * - PDF header validation
 *
 * @param req NextRequest object
 * @returns Parsed form data with file buffer and fields
 */
export async function parseFormData(req: NextRequest): Promise<ParsedFormData> {
  try {
    // Parse form data using Next.js built-in method
    const formData = await req.formData();

    // Extract the uploaded file
    const file = formData.get('file') as File;
    if (!file) {
      throw new Error('No file uploaded');
    }

    // Validate file type
    if (!isValidPdfFile(file)) {
      throw new Error('Only PDF files are allowed');
    }

    // Validate file size (50MB limit)
    const maxFileSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxFileSize) {
      throw new Error(`File size exceeds limit of ${maxFileSize / (1024 * 1024)}MB`);
    }

    // Validate file is not empty
    if (file.size === 0) {
      throw new Error('Empty files are not allowed');
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = Buffer.from(arrayBuffer);

    // Validate PDF file header (basic check)
    if (!isValidPdfBuffer(fileBuffer)) {
      throw new Error('Invalid PDF file format');
    }

    // Extract other form fields
    const fields: Record<string, string> = {};
    for (const [key, value] of formData.entries()) {
      if (key !== 'file') {
        fields[key] = typeof value === 'string' ? value : value.toString();
      }
    }

    return {
      fields,
      fileBuffer,
      fileName: file.name || 'uploaded.pdf',
      fileSize: file.size,
    };
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to parse form data');
  }
}

/**
 * Validate if the uploaded file is a valid PDF
 * @param file File object
 * @returns true if valid PDF, false otherwise
 */
function isValidPdfFile(file: File): boolean {
  // Check MIME type
  if (file.type === 'application/pdf') {
    return true;
  }

  // Check file extension
  if (file.name && file.name.toLowerCase().endsWith('.pdf')) {
    return true;
  }

  return false;
}

/**
 * Validate PDF file by checking the file header
 * @param buffer File buffer
 * @returns true if valid PDF header, false otherwise
 */
function isValidPdfBuffer(buffer: Buffer): boolean {
  // Check for PDF magic number at the beginning of the file
  // PDF files start with "%PDF-"
  if (buffer.length < 5) {
    return false;
  }

  const header = buffer.subarray(0, 5).toString('ascii');
  return header === '%PDF-';
}

/**
 * Clean up temporary file (no-op in this implementation since we don't use temp files)
 * Kept for API compatibility
 * @param filePath Path to the temporary file
 */
export async function cleanupTempFile(filePath: string): Promise<void> {
  // No-op since we're not using temporary files in this implementation
  // This function is kept for API compatibility
}
