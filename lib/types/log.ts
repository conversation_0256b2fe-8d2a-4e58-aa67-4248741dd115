// Types for processing logs

import { ProcessingLog } from '@prisma/client';

// Log entry with formatted dates for display
export interface FormattedLogEntry extends Omit<ProcessingLog, 'requestTimestamp' | 'inputDocumentDate' | 'ocrDocumentDate' | 'processedAt'> {
  requestTimestamp: string;
  inputDocumentDate: string | null;
  ocrDocumentDate: string | null;
  processedAt: string | null;
  // Additional calculated fields
  matchedFieldsPercentage?: number | null;
  // API key information
  apiKeyName?: string | null;
}

// Filter options for logs
export interface LogFilterOptions {
  requestType?: string;
  processingStatus?: string;
  startDate?: Date;
  endDate?: Date;
  vendorName?: string;
  documentNo?: string;
}

// Pagination options
export interface PaginationOptions {
  page: number;
  pageSize: number;
}

// Sorted and filtered logs response
export interface LogsResponse {
  logs: FormattedLogEntry[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
