import { OpenAPIObject } from 'openapi3-ts';

const openApiDocument: OpenAPIObject = {
  openapi: '3.0.0',
  info: {
    title: 'Invoice Matching API',
    description: 'API for processing and matching invoices and tax invoices',
    version: '1.0.0',
    contact: {
      name: 'API Support',
      email: '<EMAIL>',
    },
  },
  servers: [
    {
      url: 'https://kim-staging.happyfresh.io',
      description: 'Local development server',
    },
    {
      url: 'https://kim.happyfresh.io',
      description: 'Production server',
    },
  ],
  tags: [
    {
      name: 'Invoice Processing',
      description: 'API endpoints for processing invoices',
    },
    {
      name: 'Tax Invoice Processing',
      description: 'API endpoints for processing tax invoices',
    },
    {
      name: 'Logs',
      description: 'API endpoints for retrieving processing logs',
    },
    {
      name: 'API Keys',
      description: 'API endpoints for managing API keys (admin only)',
    },
  ],
  paths: {
    '/api/process-invoice': {
      post: {
        tags: ['Invoice Processing'],
        summary: 'Process a standard invoice from URL',
        description: 'Process a PDF invoice from a URL and match against provided data',
        operationId: 'processInvoiceUrl',
        security: [
          {
            apiKey: [],
          },
        ],
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  file_url: {
                    type: 'string',
                    format: 'uri',
                    description: 'URL to the PDF invoice file to process',
                  },
                  vendor_name: {
                    type: 'string',
                    description: 'Vendor name for matching',
                  },
                  invoice_number: {
                    type: 'string',
                    description: 'Invoice number for matching',
                  },
                  invoice_date: {
                    type: 'string',
                    format: 'date',
                    description: 'Invoice date for matching (YYYY-MM-DD)',
                  },
                  invoice_amount: {
                    type: 'number',
                    description: 'Invoice amount for matching',
                  },
                  vat_amount: {
                    type: 'number',
                    description: 'VAT amount for matching',
                  },
                },
                required: ['file_url'],
              },
            },
          },
          required: true,
        },
        responses: {
          '200': {
            description: 'Invoice processed successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/InvoiceResponse',
                },
              },
            },
          },
          '400': {
            description: 'Bad request - Missing file URL or invalid input',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Invalid or missing API key',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'API key is required',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
    '/api/process-invoice-file': {
      post: {
        tags: ['Invoice Processing'],
        summary: 'Process a standard invoice file upload',
        description: 'Upload a PDF invoice file for processing and matching against provided data',
        operationId: 'processInvoiceFile',
        security: [
          {
            apiKey: [],
          },
        ],
        requestBody: {
          content: {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary',
                    description: 'PDF invoice file to process',
                  },
                  vendor_name: {
                    type: 'string',
                    description: 'Vendor name for matching',
                  },
                  invoice_number: {
                    type: 'string',
                    description: 'Invoice number for matching',
                  },
                  invoice_date: {
                    type: 'string',
                    format: 'date',
                    description: 'Invoice date for matching (YYYY-MM-DD)',
                  },
                  invoice_amount: {
                    type: 'number',
                    description: 'Invoice amount for matching',
                  },
                  vat_amount: {
                    type: 'number',
                    description: 'VAT amount for matching',
                  },
                },
                required: ['file'],
              },
            },
          },
          required: true,
        },
        responses: {
          '200': {
            description: 'Invoice processed successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/InvoiceResponse',
                },
              },
            },
          },
          '400': {
            description: 'Bad request - Missing file or invalid input',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Invalid or missing API key',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'API key is required',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
    '/api/process-tax-invoice': {
      post: {
        tags: ['Tax Invoice Processing'],
        summary: 'Process a tax invoice from URL',
        description: 'Process a PDF tax invoice from a URL and match against provided data',
        operationId: 'processTaxInvoiceUrl',
        security: [
          {
            apiKey: [],
          },
        ],
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  file_url: {
                    type: 'string',
                    format: 'uri',
                    description: 'URL to the PDF tax invoice file to process',
                  },
                  vendor_name: {
                    type: 'string',
                    description: 'Vendor name for matching',
                  },
                  tax_invoice_number: {
                    type: 'string',
                    description: 'Tax invoice number for matching',
                  },
                  tax_invoice_date: {
                    type: 'string',
                    format: 'date',
                    description: 'Tax invoice date for matching (YYYY-MM-DD)',
                  },
                  invoice_amount: {
                    type: 'number',
                    description: 'Invoice amount for matching',
                  },
                  vat_amount: {
                    type: 'number',
                    description: 'VAT amount for matching',
                  },
                },
                required: ['file_url'],
              },
            },
          },
          required: true,
        },
        responses: {
          '200': {
            description: 'Tax invoice processed successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/TaxInvoiceResponse',
                },
              },
            },
          },
          '400': {
            description: 'Bad request - Missing file URL or invalid input',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Invalid or missing API key',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'API key is required',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
    '/api/process-tax-invoice-file': {
      post: {
        tags: ['Tax Invoice Processing'],
        summary: 'Process a tax invoice file upload',
        description: 'Upload a PDF tax invoice file for processing and matching against provided data',
        operationId: 'processTaxInvoiceFile',
        security: [
          {
            apiKey: [],
          },
        ],
        requestBody: {
          content: {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary',
                    description: 'PDF tax invoice file to process',
                  },
                  vendor_name: {
                    type: 'string',
                    description: 'Vendor name for matching',
                  },
                  tax_invoice_number: {
                    type: 'string',
                    description: 'Tax invoice number for matching',
                  },
                  tax_invoice_date: {
                    type: 'string',
                    format: 'date',
                    description: 'Tax invoice date for matching (YYYY-MM-DD)',
                  },
                  invoice_amount: {
                    type: 'number',
                    description: 'Invoice amount for matching',
                  },
                  vat_amount: {
                    type: 'number',
                    description: 'VAT amount for matching',
                  },
                },
                required: ['file'],
              },
            },
          },
          required: true,
        },
        responses: {
          '200': {
            description: 'Tax invoice processed successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/TaxInvoiceResponse',
                },
              },
            },
          },
          '400': {
            description: 'Bad request - Missing file or invalid input',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Invalid or missing API key',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'API key is required',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
    '/api/admin/api-keys': {
      get: {
        tags: ['API Keys'],
        summary: 'List API keys',
        description: 'Get a list of all API keys (admin only)',
        operationId: 'getApiKeys',
        responses: {
          '200': {
            description: 'API keys retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    apiKeys: {
                      type: 'array',
                      items: {
                        $ref: '#/components/schemas/ApiKey',
                      },
                    },
                  },
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Authentication required',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'Unauthorized',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
        },
      },
      post: {
        tags: ['API Keys'],
        summary: 'Create API key',
        description: 'Create a new API key (admin only)',
        operationId: 'createApiKey',
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  name: {
                    type: 'string',
                    description: 'Name of the API key',
                  },
                  description: {
                    type: 'string',
                    description: 'Description of the API key',
                  },
                },
                required: ['name'],
              },
            },
          },
          required: true,
        },
        responses: {
          '201': {
            description: 'API key created successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/NewApiKey',
                },
              },
            },
          },
          '400': {
            description: 'Bad request - Missing required fields',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Authentication required',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'Unauthorized',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/admin/api-keys/{id}': {
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'API key ID',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      get: {
        tags: ['API Keys'],
        summary: 'Get API key',
        description: 'Get a specific API key by ID (admin only)',
        operationId: 'getApiKey',
        responses: {
          '200': {
            description: 'API key retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    apiKey: {
                      $ref: '#/components/schemas/ApiKey',
                    },
                  },
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Authentication required',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'Unauthorized',
                    },
                  },
                },
              },
            },
          },
          '404': {
            description: 'API key not found',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'API key not found',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
        },
      },
      patch: {
        tags: ['API Keys'],
        summary: 'Update API key',
        description: 'Update an API key (activate/deactivate) (admin only)',
        operationId: 'updateApiKey',
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  isActive: {
                    type: 'boolean',
                    description: 'Whether the API key is active',
                  },
                },
                required: ['isActive'],
              },
            },
          },
          required: true,
        },
        responses: {
          '200': {
            description: 'API key updated successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                    },
                    name: {
                      type: 'string',
                    },
                    isActive: {
                      type: 'boolean',
                    },
                  },
                },
              },
            },
          },
          '400': {
            description: 'Bad request - Missing required fields',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Authentication required',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'Unauthorized',
                    },
                  },
                },
              },
            },
          },
          '404': {
            description: 'API key not found',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'API key not found',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
        },
      },
      delete: {
        tags: ['API Keys'],
        summary: 'Delete API key',
        description: 'Delete an API key (admin only)',
        operationId: 'deleteApiKey',
        responses: {
          '200': {
            description: 'API key deleted successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: {
                      type: 'boolean',
                      example: true,
                    },
                  },
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Authentication required',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'Unauthorized',
                    },
                  },
                },
              },
            },
          },
          '404': {
            description: 'API key not found',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'API key not found',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/logs': {
      get: {
        tags: ['Logs'],
        summary: 'Get processing logs',
        description: 'Retrieve processing logs with optional filtering and pagination',
        operationId: 'getLogs',
        security: [
          {
            apiKey: [],
          },
        ],
        parameters: [
          {
            name: 'requestType',
            in: 'query',
            description: 'Filter by request type (invoice or taxInvoice)',
            schema: {
              type: 'string',
              enum: ['invoice', 'taxInvoice'],
            },
          },
          {
            name: 'processingStatus',
            in: 'query',
            description: 'Filter by processing status (success, failed, processing)',
            schema: {
              type: 'string',
              enum: ['success', 'failed', 'processing'],
            },
          },
          {
            name: 'startDate',
            in: 'query',
            description: 'Filter by start date (ISO format)',
            schema: {
              type: 'string',
              format: 'date-time',
            },
          },
          {
            name: 'endDate',
            in: 'query',
            description: 'Filter by end date (ISO format)',
            schema: {
              type: 'string',
              format: 'date-time',
            },
          },
          {
            name: 'vendorName',
            in: 'query',
            description: 'Filter by vendor name',
            schema: {
              type: 'string',
            },
          },
          {
            name: 'documentNo',
            in: 'query',
            description: 'Filter by document number',
            schema: {
              type: 'string',
            },
          },
          {
            name: 'page',
            in: 'query',
            description: 'Page number for pagination',
            schema: {
              type: 'integer',
              default: 1,
              minimum: 1,
            },
          },
          {
            name: 'pageSize',
            in: 'query',
            description: 'Number of items per page',
            schema: {
              type: 'integer',
              default: 10,
              minimum: 1,
              maximum: 100,
            },
          },
        ],
        responses: {
          '200': {
            description: 'Processing logs retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/LogsResponse',
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized - Invalid or missing API key',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      example: 'API key is required',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  components: {
    securitySchemes: {
      apiKey: {
        type: 'apiKey',
        in: 'header',
        name: 'X-API-Key',
        description: 'API key for authentication. Get your API key from the admin dashboard.',
      },
    },
    schemas: {
      FieldMatch: {
        type: 'object',
        properties: {
          input_value: {
            type: 'string',
            description: 'Value provided by the user',
          },
          ocr_value: {
            type: 'string',
            description: 'Value extracted from the document',
          },
          status: {
            type: 'string',
            enum: ['matched', 'mismatched', 'not_found'],
            description: 'Status of the field matching',
          },
        },
      },
      MatchingSummary: {
        type: 'object',
        properties: {
          total_fields: {
            type: 'integer',
            description: 'Total number of fields checked',
          },
          matched: {
            type: 'integer',
            description: 'Number of fields that matched',
          },
          mismatched: {
            type: 'integer',
            description: 'Number of fields that did not match',
          },
          not_found: {
            type: 'integer',
            description: 'Number of fields not found in the document',
          },
        },
      },
      InvoiceMatchingFields: {
        type: 'object',
        properties: {
          vendor_name: {
            $ref: '#/components/schemas/FieldMatch',
          },
          invoice_number: {
            $ref: '#/components/schemas/FieldMatch',
          },
          invoice_date: {
            $ref: '#/components/schemas/FieldMatch',
          },
          invoice_amount: {
            $ref: '#/components/schemas/FieldMatch',
          },
          vat_amount: {
            $ref: '#/components/schemas/FieldMatch',
          },
        },
      },
      TaxInvoiceMatchingFields: {
        type: 'object',
        properties: {
          vendor_name: {
            $ref: '#/components/schemas/FieldMatch',
          },
          tax_invoice_number: {
            $ref: '#/components/schemas/FieldMatch',
          },
          tax_invoice_date: {
            $ref: '#/components/schemas/FieldMatch',
          },
          invoice_amount: {
            $ref: '#/components/schemas/FieldMatch',
          },
          vat_amount: {
            $ref: '#/components/schemas/FieldMatch',
          },
        },
      },
      MatchingResult: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            description: 'Whether the processing was successful',
          },
          summary: {
            $ref: '#/components/schemas/MatchingSummary',
          },
          fields: {
            oneOf: [
              { $ref: '#/components/schemas/InvoiceMatchingFields' },
              { $ref: '#/components/schemas/TaxInvoiceMatchingFields' },
            ],
          },
          is_colored: {
            type: 'boolean',
            description: 'Whether the PDF contains color',
          },
          color_pages: {
            type: 'array',
            items: {
              type: 'integer',
            },
            description: 'Array of page numbers that contain color',
          },
          total_pages: {
            type: 'integer',
            description: 'Total number of pages in the PDF',
          },
        },
      },
      InvoiceResponse: {
        type: 'object',
        properties: {
          processing_id: {
            type: 'string',
            description: 'Unique ID for the processing request',
          },
          results: {
            $ref: '#/components/schemas/MatchingResult',
          },
          log_id: {
            type: 'string',
            description: 'ID of the processing log entry',
          },
        },
      },
      TaxInvoiceResponse: {
        type: 'object',
        properties: {
          processing_id: {
            type: 'string',
            description: 'Unique ID for the processing request',
          },
          results: {
            $ref: '#/components/schemas/MatchingResult',
          },
          log_id: {
            type: 'string',
            description: 'ID of the processing log entry',
          },
        },
      },
      ErrorResponse: {
        type: 'object',
        properties: {
          processing_id: {
            type: 'string',
            description: 'Error ID',
          },
          results: {
            $ref: '#/components/schemas/MatchingResult',
          },
          error: {
            type: 'string',
            description: 'Error message',
          },
        },
      },
      FormattedLogEntry: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Unique ID of the log entry',
          },
          requestType: {
            type: 'string',
            enum: ['invoice', 'taxInvoice'],
            description: 'Type of request',
          },
          processingStatus: {
            type: 'string',
            enum: ['success', 'failed', 'processing'],
            description: 'Status of the processing',
          },
          fileName: {
            type: 'string',
            description: 'Name of the uploaded file',
          },
          fileSize: {
            type: 'integer',
            description: 'Size of the file in bytes',
          },
          requestTimestamp: {
            type: 'string',
            format: 'date-time',
            description: 'Timestamp of the request',
          },

          processedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Timestamp when the processing was completed',
          },
          matchedFieldsPercentage: {
            type: 'number',
            description: 'Percentage of fields that matched',
          },
        },
      },
      LogsResponse: {
        type: 'object',
        properties: {
          logs: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/FormattedLogEntry',
            },
          },
          total: {
            type: 'integer',
            description: 'Total number of logs matching the filter criteria',
          },
          page: {
            type: 'integer',
            description: 'Current page number',
          },
          pageSize: {
            type: 'integer',
            description: 'Number of items per page',
          },
          totalPages: {
            type: 'integer',
            description: 'Total number of pages',
          },
        },
      },
      ApiKey: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Unique ID of the API key',
          },
          name: {
            type: 'string',
            description: 'Name of the API key',
          },
          description: {
            type: 'string',
            description: 'Description of the API key',
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Timestamp when the API key was created',
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Timestamp when the API key was last updated',
          },
          lastUsedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Timestamp when the API key was last used',
          },
          isActive: {
            type: 'boolean',
            description: 'Whether the API key is active',
          },
          createdBy: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                description: 'Name of the user who created the API key',
              },
              email: {
                type: 'string',
                description: 'Email of the user who created the API key',
              },
            },
          },
        },
      },
      NewApiKey: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Unique ID of the API key',
          },
          name: {
            type: 'string',
            description: 'Name of the API key',
          },
          description: {
            type: 'string',
            description: 'Description of the API key',
          },
          key: {
            type: 'string',
            description: 'The API key value (only shown once when created)',
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Timestamp when the API key was created',
          },
        },
      },
    },
  },
};

export default openApiDocument;
