"postgresql://postgres.axyujusllpfbbgewabta:<EMAIL>:5432/invoice_matching_staging"

{
  "database": "invoice_matching_staging",
  "host": "kim-postgres-staging.postgres.database.azure.com",
  "user": "kimpgadmin"
  kimZ54pTZ0Ym5pg
}

psql -h kim-postgres-staging.postgres.database.azure.com -p 5432 -d invoice_matching_staging -U kimpgadmin

pg_dump -h aws-0-ap-southeast-1.pooler.supabase.com -p 5432 -Upostgres.axyujusllpfbbgewabta -F p invoice_matching_staging > temp/invoice_matching_staging_dump.sql

## Dump
docker run --rm \
  -e PGPASSWORD="VHGe4uumpD8IbwvVsdW8fTdcNWNu9vhx" \
  -v "$(pwd)/temp:/temp" \
  postgres:16 \
  pg_dump -h aws-0-ap-southeast-1.pooler.supabase.com -p 5432 -U postgres.axyujusllpfbbgewabta -F c -f /temp/invoice_matching_staging.custom invoice_matching_staging


## Load
docker run --rm \
  -e PGHOST="kim-postgres-staging.postgres.database.azure.com" \
  -e PGUSER="kimpgadmin" \
  -e PGPORT=5432 \
  -e PGDATABASE="invoice_matching_staging" \
  -e PGPASSWORD="kimZ54pTZ0Ym5pg" \
  -v "$(pwd)/temp:/temp" \
  --network host \
  postgres:16 \
  pg_restore -h $PGHOST -p $PGPORT -U $PGUSER -d $PGDATABASE -j 1 /temp/invoice_matching_staging.custom

  
  pg_restore -h kim-postgres-staging.postgres.database.azure.com -p 5433 -U kimpgadmin -d invoice_matching_staging -j 1 /temp/invoice_matching_staging.custom


Azure api key
ki_c510f151e443144d.7511772af32cef830c639b328cf37b9a5807d8eedf5ec902539561fda10b0f75