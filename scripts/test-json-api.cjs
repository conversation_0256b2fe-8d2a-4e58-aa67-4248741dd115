#!/usr/bin/env node

const fetch = require('node-fetch');

// Check if a file URL is provided
if (process.argv.length < 3) {
  console.error('Please provide a PDF file URL');
  console.error('Usage: node test-json-api.cjs <pdf-file-url> [endpoint] [api-key]');
  console.error('Available endpoints:');
  console.error('  process-invoice-json - Invoice JSON endpoint');
  console.error('  process-tax-invoice-json - Tax invoice JSON endpoint');
  process.exit(1);
}

// Get the file URL from command line arguments
const fileUrl = process.argv[2];
const endpoint = process.argv[3] || 'process-invoice-json';
const apiKey = process.argv[4] || 'ki_c718b1aeb9ba4705.f29245776484bae3b38a9d340a446a61424dc146124d42ebdc197766958053b7';

// Create the JSON payload
let jsonData;
if (endpoint.includes('tax-invoice')) {
  jsonData = {
    file_url: fileUrl,
    vendor_name: 'Test Vendor',
    tax_invoice_number: 'TAX-123',
    invoice_date: '2023-01-01',
    invoice_amount: '1000',
    vat_amount: '100'
  };
} else {
  jsonData = {
    file_url: fileUrl,
    vendor_name: 'Test Vendor',
    invoice_number: 'INV-123',
    invoice_date: '2023-01-01',
    invoice_amount: '1000',
    vat_amount: '100'
  };
}

// Log the JSON data being sent
console.log('JSON data being sent:');
console.log(JSON.stringify(jsonData, null, 2));

// Set the API URL
const apiUrl = `http://localhost:3000/api/${endpoint}`;

console.log(`Sending request to: ${apiUrl}`);
console.log(`Using API key: ${apiKey ? '******' + apiKey.slice(-4) : 'None'}`);

// Send the request
(async () => {
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      body: JSON.stringify(jsonData),
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
      },
    });

    // Get the response body
    const data = await response.json();

    // Log the response
    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error);
  }
})();
