#!/usr/bin/env node

/**
 * Command-line script to detect color in PDF files
 * 
 * Usage:
 *   node detect-color.js <path-to-pdf-file-or-folder>
 * 
 * Examples:
 *   node detect-color.js ./sample.pdf
 *   node detect-color.js ./pdf-folder
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Check if TypeScript is compiled
try {
  require.resolve('../dist/lib/api/color-detection');
} catch (error) {
  console.log('TypeScript files need to be compiled first. Compiling...');
  try {
    execSync('npx tsc', { stdio: 'inherit' });
  } catch (error) {
    console.error('Failed to compile TypeScript files:', error.message);
    process.exit(1);
  }
}

// Import the color detection function
const { detectColor } = require('../dist/lib/api/color-detection');

// Check if a path is provided
if (process.argv.length < 3) {
  console.error('Please provide a path to a PDF file or folder.');
  console.error('Usage: node detect-color.js <path-to-pdf-file-or-folder>');
  process.exit(1);
}

// Get the file or folder path from command line arguments
const inputPath = process.argv[2];

// Check if the path exists
if (!fs.existsSync(inputPath)) {
  console.error(`Path not found: ${inputPath}`);
  process.exit(1);
}

// Function to process a single PDF file
async function processPdf(filePath) {
  console.log(`\nProcessing: ${filePath}`);
  
  try {
    // Check if the file is a PDF
    if (!filePath.toLowerCase().endsWith('.pdf')) {
      console.log('Skipping non-PDF file');
      return;
    }
    
    // Read the file
    const pdfData = fs.readFileSync(filePath);
    
    // Start timer
    const startTime = Date.now();
    
    // Analyze the PDF
    const results = await detectColor(pdfData);
    
    // Calculate processing time
    const processingTime = ((Date.now() - startTime) / 1000).toFixed(2);
    
    // Display results
    console.log('\n=== PDF Color Analysis Results ===\n');
    console.log(`File: ${path.basename(filePath)}`);
    console.log(`Total Pages: ${results.total_pages}`);
    console.log(`Contains Color: ${results.is_colored ? 'Yes' : 'No'}`);
    console.log(`Color Pages: ${results.color_pages.length > 0 ? results.color_pages.join(', ') : 'None'}`);
    console.log(`Processing Time: ${processingTime} seconds`);
    
    return results;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

// Function to process a folder of PDF files
async function processFolder(folderPath) {
  console.log(`\nProcessing folder: ${folderPath}`);
  
  try {
    // Get all files in the folder
    const files = fs.readdirSync(folderPath);
    
    // Filter for PDF files
    const pdfFiles = files.filter(file => file.toLowerCase().endsWith('.pdf'));
    
    console.log(`Found ${pdfFiles.length} PDF files`);
    
    // Process each PDF file
    const results = [];
    for (const file of pdfFiles) {
      const filePath = path.join(folderPath, file);
      const result = await processPdf(filePath);
      if (result) {
        results.push({
          file: path.basename(filePath),
          ...result
        });
      }
    }
    
    // Display summary
    console.log('\n=== Summary ===\n');
    console.log(`Total PDFs processed: ${results.length}`);
    console.log(`PDFs with color: ${results.filter(r => r.is_colored).length}`);
    console.log(`PDFs without color: ${results.filter(r => !r.is_colored).length}`);
    
    // Save results to JSON file
    const outputPath = path.join(process.cwd(), 'color-detection-results.json');
    fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
    console.log(`\nDetailed results saved to: ${outputPath}`);
  } catch (error) {
    console.error(`Error processing folder ${folderPath}:`, error.message);
  }
}

// Main function
async function main() {
  const stats = fs.statSync(inputPath);
  
  if (stats.isDirectory()) {
    // Process all PDF files in the folder
    await processFolder(inputPath);
  } else {
    // Process a single PDF file
    await processPdf(inputPath);
  }
}

// Run the main function
main().catch(error => {
  console.error('Error:', error.message);
  process.exit(1);
});
