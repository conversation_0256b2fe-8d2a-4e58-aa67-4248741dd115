#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// Check if a file path is provided
if (process.argv.length < 3) {
  console.error('Please provide a PDF file path');
  console.error('Usage: node test-file-upload.cjs <pdf-file-path> [endpoint] [api-key]');
  console.error('Available endpoints:');
  console.error('  process-invoice-file - Original invoice file endpoint');
  console.error('  process-tax-invoice-file - Original tax invoice file endpoint');
  console.error('  process-invoice-file-direct - Direct invoice file endpoint');
  console.error('  process-tax-invoice-file-direct - Direct tax invoice file endpoint');
  process.exit(1);
}

// Get the file path from command line arguments
const filePath = process.argv[2];
const endpoint = process.argv[3] || 'process-invoice-file';

// Check if the file exists
if (!fs.existsSync(filePath)) {
  console.error(`File not found: ${filePath}`);
  process.exit(1);
}

// Check if the file is a PDF
if (!filePath.toLowerCase().endsWith('.pdf')) {
  console.error('The file must be a PDF');
  process.exit(1);
}

// Create a form data object
const form = new FormData();

// Important: Add the fields BEFORE the file to ensure they're properly parsed
// Add required fields based on the endpoint
if (endpoint.includes('tax-invoice')) {
  form.append('vendor_name', 'Test Vendor');
  form.append('tax_invoice_number', 'TAX-123');
  form.append('tax_invoice_date', '2023-01-01');
  form.append('invoice_amount', '1000');
  form.append('vat_amount', '100');
} else {
  form.append('vendor_name', 'Test Vendor');
  form.append('invoice_number', 'INV-123');
  form.append('invoice_date', '2023-01-01');
  form.append('invoice_amount', '1000');
  form.append('vat_amount', '100');
}

// Add the file to the form data AFTER the fields
form.append('file', fs.createReadStream(filePath));

// Log the form data being sent
console.log('Form data being sent:');
console.log('- vendor_name: Test Vendor');
if (endpoint.includes('tax-invoice')) {
  console.log('- tax_invoice_number: TAX-123');
  console.log('- tax_invoice_date: 2023-01-01');
} else {
  console.log('- invoice_number: INV-123');
  console.log('- invoice_date: 2023-01-01');
}
console.log('- invoice_amount: 1000');
console.log('- vat_amount: 100');
console.log('- file:', filePath);

// Set the API URL
const apiUrl = `http://localhost:3000/api/${endpoint}`;

// Set the API key
const apiKey = process.argv[4] || 'ki_c718b1aeb9ba4705.f29245776484bae3b38a9d340a446a61424dc146124d42ebdc197766958053b7';

console.log(`Uploading file: ${filePath}`);
console.log(`To endpoint: ${apiUrl}`);
console.log(`Using API key: ${apiKey ? '******' + apiKey.slice(-4) : 'None'}`);

// Send the request
(async () => {
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      body: form,
      headers: {
        ...form.getHeaders(),
        'x-api-key': apiKey,
      },
    });

    // Get the response body
    const data = await response.json();

    // Log the response
    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error);
  }
})();
