const bcrypt = require("bcryptjs");
const { PrismaClient } = require("@prisma/client");
const readline = require("readline");

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to check if a user exists
async function checkUserExists(email) {
  console.log("Checking if user exists...");
  const prismaCheck = new PrismaClient();

  try {
    const existingUser = await prismaCheck.adminUser.findUnique({
      where: { email }
    });
    return existingUser;
  } catch (error) {
    console.error("Error checking user existence:", error.message);
    throw error;
  } finally {
    await prismaCheck.$disconnect();
  }
}

// Function to create a user
async function createUser(userData) {
  console.log("Creating new user...");
  const prismaCreate = new PrismaClient();

  try {
    const user = await prismaCreate.adminUser.create({
      data: userData
    });
    return user;
  } catch (error) {
    console.error("Error creating user:", error.message);
    throw error;
  } finally {
    await prismaCreate.$disconnect();
  }
}

async function createAdminUser() {
  console.log("Create Admin User");
  console.log("=================");

  const name = await new Promise(resolve => {
    rl.question("Name: ", resolve);
  });

  const email = await new Promise(resolve => {
    rl.question("Email: ", resolve);
  });

  const password = await new Promise(resolve => {
    rl.question("Password: ", resolve);
  });

  try {
    // Using bcryptjs instead of bcrypt
    const passwordHash = await bcrypt.hash(password, 10);

    // Check if user already exists
    let existingUser;
    try {
      existingUser = await checkUserExists(email);
    } catch (error) {
      console.error("Failed to check if user exists:", error);
      rl.close();
      return;
    }

    if (existingUser) {
      console.log(`User with email ${email} already exists.`);
      rl.close();
      return;
    }

    // Create the user
    try {
      const user = await createUser({
        name,
        email,
        passwordHash,
        role: "admin"
      });

      console.log(`Admin user created successfully: ${user.email}`);
    } catch (error) {
      console.error("Failed to create user:", error);
    }

    rl.close();
  } catch (error) {
    console.error("Error in admin user creation process:", error);
    rl.close();
  }
}

createAdminUser();
