#!/usr/bin/env node

/**
 * Simple script to test enhanced color detection on PDF files
 * This script bypasses TypeScript compilation errors and compiles just the needed file
 *
 * Usage:
 *   node test-enhanced-color.js <path-to-pdf-file-or-folder>
 *
 * Examples:
 *   node test-enhanced-color.js ./sample.pdf
 *   node test-enhanced-color.js ./pdf-folder
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Create a temporary directory for compilation if it doesn't exist
const tempDir = path.join(__dirname, '../temp');
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

// Path to the source TypeScript file
const sourceTsPath = path.join(__dirname, '../lib/api/enhanced-color-detection.ts');

// Path where the compiled JS file will be placed
const compiledJsPath = path.join(tempDir, 'enhanced-color-detection.js');

// Check if the source file exists
if (!fs.existsSync(sourceTsPath)) {
  console.error(`Source file not found: ${sourceTsPath}`);
  process.exit(1);
}

// Compile the TypeScript file directly to our temp directory
console.log('Compiling enhanced-color-detection.ts...');
try {
  // Create a temporary tsconfig.json file
  const tsConfig = {
    compilerOptions: {
      target: "es2020",
      module: "commonjs",
      esModuleInterop: true,
      skipLibCheck: true,
      outDir: tempDir,
      strict: false,
      noImplicitAny: false
    },
    include: [sourceTsPath]
  };

  // Write the tsconfig to a temporary file
  const tsConfigPath = path.join(tempDir, 'tsconfig.json');
  fs.writeFileSync(tsConfigPath, JSON.stringify(tsConfig, null, 2));

  // Run the TypeScript compiler with our custom config
  execSync(`npx tsc -p ${tsConfigPath}`, { stdio: 'inherit' });
  console.log('Compilation successful!');
} catch (error) {
  console.error('Failed to compile:', error.message);
  process.exit(1);
}

// Check if the compiled file exists
if (!fs.existsSync(compiledJsPath)) {
  console.error(`Compiled file not found: ${compiledJsPath}`);
  process.exit(1);
}

// Import the compiled JavaScript
let detectColor;
try {
  detectColor = require(compiledJsPath).detectColor;

  if (!detectColor || typeof detectColor !== 'function') {
    throw new Error('detectColor function not found in the compiled file');
  }
} catch (error) {
  console.error('Error importing enhanced-color-detection.js:', error.message);
  process.exit(1);
}

// Check if a path is provided
if (process.argv.length < 3) {
  console.error('Please provide a path to a PDF file or folder.');
  console.error('Usage: node test-enhanced-color.js <path-to-pdf-file-or-folder>');
  process.exit(1);
}

// Get the file or folder path from command line arguments
const inputPath = process.argv[2];

// Check if the path exists
if (!fs.existsSync(inputPath)) {
  console.error(`Path not found: ${inputPath}`);
  process.exit(1);
}

// Function to process a single PDF file
async function processPdf(filePath) {
  console.log(`\nProcessing: ${filePath}`);

  try {
    // Check if the file is a PDF
    if (!filePath.toLowerCase().endsWith('.pdf')) {
      console.log('Skipping non-PDF file');
      return null;
    }

    // Read the file
    const pdfData = fs.readFileSync(filePath);

    // Start timer
    const startTime = Date.now();

    // Analyze the PDF using enhanced color detection
    const results = await detectColor(pdfData);

    // Calculate processing time
    const processingTime = (Date.now() - startTime) / 1000;

    // Display results
    console.log('\n=== Enhanced PDF Color Analysis Results ===\n');
    console.log(`File: ${path.basename(filePath)}`);
    console.log(`Total Pages: ${results.total_pages}`);
    console.log(`Contains Color: ${results.is_colored ? 'Yes' : 'No'}`);
    console.log(`Color Pages: ${results.color_pages.length > 0 ? results.color_pages.join(', ') : 'None'}`);
    console.log(`Detection Method: ${results.method || 'Unknown'}`);
    console.log(`Confidence: ${results.confidence || 'Unknown'}`);
    console.log(`Processing Time: ${processingTime.toFixed(2)} seconds`);

    return {
      file: path.basename(filePath),
      ...results,
      processing_time: processingTime
    };
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error instanceof Error ? error.message : String(error));
    return null;
  }
}

// Function to process a folder of PDF files
async function processFolder(folderPath) {
  console.log(`\nProcessing folder: ${folderPath}`);

  try {
    // Get all files in the folder
    const files = fs.readdirSync(folderPath);

    // Filter for PDF files
    const pdfFiles = files.filter(file => file.toLowerCase().endsWith('.pdf'));

    console.log(`Found ${pdfFiles.length} PDF files`);

    // Process each PDF file
    const results = [];
    for (const file of pdfFiles) {
      const filePath = path.join(folderPath, file);
      const result = await processPdf(filePath);
      if (result) {
        results.push(result);
      }
    }

    // Display summary
    console.log('\n=== Summary ===\n');
    console.log(`Total PDFs processed: ${results.length}`);
    console.log(`PDFs with color: ${results.filter(r => r.is_colored).length}`);
    console.log(`PDFs without color: ${results.filter(r => !r.is_colored).length}`);

    // Save results to JSON file
    const outputPath = path.join(process.cwd(), 'enhanced-color-detection-results.json');
    fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
    console.log(`\nDetailed results saved to: ${outputPath}`);
  } catch (error) {
    console.error(`Error processing folder ${folderPath}:`, error instanceof Error ? error.message : String(error));
  }
}

// Main function
async function main() {
  const stats = fs.statSync(inputPath);

  if (stats.isDirectory()) {
    // Process all PDF files in the folder
    await processFolder(inputPath);
  } else {
    // Process a single PDF file
    await processPdf(inputPath);
  }
}

// Run the main function
main().catch(error => {
  console.error('Error:', error instanceof Error ? error.message : String(error));
  process.exit(1);
});
