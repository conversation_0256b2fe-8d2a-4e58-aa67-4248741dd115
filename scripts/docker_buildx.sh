#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Set default values
REGISTRY_NAME="happyfresh"
AZURE_IMAGE_NAME="happyfresh.azurecr.io/ext/ki/invoice-matching"
GITLAB_IMAGE_NAME="registry-gitlab.happyfresh.net/hf/tpd/rainmakers/ki-invoice-matching"
TAG="latest"
PLATFORMS="linux/amd64"
DEPLOY=false
CONTAINER_APP_NAME="kim-staging"
RESOURCE_GROUP="kidemo"

# Print usage information
print_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Build and push Docker image to Azure Container Registry using buildx"
    echo ""
    echo "Options:"
    echo "  -t, --tag TAG             Tag for the Docker image (default: latest)"
    echo "  -d, --deploy              Enable deployment to Azure Container App (default: false)"
    echo "  -a, --app NAME            Container App name (default: $CONTAINER_APP_NAME)"
    echo "  -g, --resource-group RG   Resource group name (default: $RESOURCE_GROUP)"
    echo "  -h, --help                Show this help message and exit"
    echo ""
    echo "Examples:"
    echo "  $0 -t v1.0.0"
    echo "  $0 --tag v1.0.0 --deploy"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -d|--deploy)
            DEPLOY=true
            shift
            ;;
        -a|--app)
            CONTAINER_APP_NAME="$2"
            shift 2
            ;;
        -g|--resource-group)
            RESOURCE_GROUP="$2"
            shift 2
            ;;
        -h|--help)
            print_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option $1"
            print_usage
            exit 1
            ;;
    esac
done

# Set default values if not provided
if [ -z "$CONTAINER_APP_NAME" ]; then
    CONTAINER_APP_NAME="kim-staging"
fi

if [ -z "$RESOURCE_GROUP" ]; then
    RESOURCE_GROUP="kidemo"
fi

echo "🔧 Using Container App: $CONTAINER_APP_NAME"
echo "🔧 Using Resource Group: $RESOURCE_GROUP"

# Main function
main() {
    echo "🔐 Logging in to Azure..."
    if ! az login --use-device-code; then
        echo "❌ Failed to log in to Azure"
        exit 1
    fi

    echo "🔑 Authenticating with Azure Container Registry..."
    if ! az acr login --name "$REGISTRY_NAME"; then
        echo "❌ Failed to log in to ACR"
        exit 1
    fi

    echo "🔑 Logging in to GitLab Container Registry..."
    if ! docker login registry-gitlab.happyfresh.net; then
        echo "❌ Failed to log in to GitLab Container Registry"
        exit 1
    fi

    echo "🚀 Building image with tag: $TAG"
    echo "📦 Platforms: $PLATFORMS"

    # Build and push the image
    if ! docker buildx build \
        --platform "$PLATFORMS" \
        -t "${AZURE_IMAGE_NAME}:${TAG}" \
        -t "${GITLAB_IMAGE_NAME}:${TAG}" \
        --push \
        . ; then
        echo "❌ Build failed"
        exit 1
    fi

    echo "✅ Successfully built and pushed images:"
    echo "   - ${AZURE_IMAGE_NAME}:${TAG}"
    echo "   - ${GITLAB_IMAGE_NAME}:${TAG}"

    # Deploy to Azure Container App if requested
    if [ "$DEPLOY" = true ]; then
        echo "🚀 Deploying to Azure Container App: $CONTAINER_APP_NAME"
        if ! az containerapp update \
            --name "$CONTAINER_APP_NAME" \
            --resource-group "$RESOURCE_GROUP" \
            --image "${AZURE_IMAGE_NAME}:${TAG}"; then
            echo "❌ Failed to update Container App"
            exit 1
        fi
        echo "✅ Successfully deployed ${IMAGE_NAME}:${TAG} to $CONTAINER_APP_NAME"
    fi
}

# Run the main function
main "$@"