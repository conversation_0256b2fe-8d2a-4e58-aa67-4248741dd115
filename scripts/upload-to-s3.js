#!/usr/bin/env node

/**
 * Script to upload files from two local folders to S3 bucket
 *
 * Usage:
 *   node upload-to-s3.js <local-folder1> <local-folder2> <s3-bucket-name> <s3-folder1> <s3-folder2>
 *
 * Example:
 *   node upload-to-s3.js ./invoices ./tax-invoices my-bucket invoices tax-invoices
 */

const fs = require('fs');
const path = require('path');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');

// Check if required arguments are provided
if (process.argv.length < 7) {
  console.error('Insufficient arguments provided.');
  console.error('Usage: node upload-to-s3.js <local-folder1> <local-folder2> <s3-bucket-name> <s3-folder1> <s3-folder2>');
  process.exit(1);
}

// Get command line arguments
const localFolder1 = process.argv[2];
const localFolder2 = process.argv[3];
const bucketName = process.argv[4];
const s3Folder1 = process.argv[5];
const s3Folder2 = process.argv[6];

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'ap-southeast-1'
});

// Function to upload a single file to S3
async function uploadFileToS3(filePath, s3Key) {
  try {
    const fileContent = fs.readFileSync(filePath);
    const params = {
      Bucket: bucketName,
      Key: s3Key,
      Body: fileContent,
      ContentType: getContentType(filePath)
    };

    await s3Client.send(new PutObjectCommand(params));
    console.log(`Successfully uploaded ${filePath} to s3://${bucketName}/${s3Key}`);
    return true;
  } catch (error) {
    console.error(`Error uploading ${filePath}:`, error.message);
    return false;
  }
}

// Function to get content type based on file extension
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  switch (ext) {
    case '.pdf': return 'application/pdf';
    case '.jpg':
    case '.jpeg': return 'image/jpeg';
    case '.png': return 'image/png';
    case '.txt': return 'text/plain';
    default: return 'application/octet-stream';
  }
}

// Function to process all files in a directory and upload to a specific S3 folder
async function processDirectory(dirPath, s3FolderPath) {
  console.log(`\nProcessing directory: ${dirPath} -> s3://${bucketName}/${s3FolderPath}`);

  try {
    // Check if directory exists
    if (!fs.existsSync(dirPath)) {
      console.error(`Directory not found: ${dirPath}`);
      return { total: 0, success: 0, failure: 0 };
    }

    // Get all files in the directory
    const files = fs.readdirSync(dirPath);

    if (files.length === 0) {
      console.log('No files found in the directory.');
      return { total: 0, success: 0, failure: 0 };
    }

    console.log(`Found ${files.length} files`);

    // Upload each file to S3 folder
    let successCount = 0;
    let failureCount = 0;

    for (const file of files) {
      const filePath = path.join(dirPath, file);

      // Skip directories
      if (fs.statSync(filePath).isDirectory()) {
        console.log(`Skipping directory: ${filePath}`);
        continue;
      }

      // Upload to S3 folder
      const s3Key = `${s3FolderPath}/${file}`;
      const success = await uploadFileToS3(filePath, s3Key);

      if (success) {
        successCount++;
      } else {
        failureCount++;
      }
    }

    return {
      total: files.length,
      success: successCount,
      failure: failureCount
    };

  } catch (error) {
    console.error(`Error processing directory ${dirPath}:`, error.message);
    return { total: 0, success: 0, failure: 0 };
  }
}

// Main function
async function main() {
  console.log(`Starting upload process...`);
  console.log(`Local folders: ${localFolder1}, ${localFolder2}`);
  console.log(`S3 bucket: ${bucketName}`);
  console.log(`S3 folders: ${s3Folder1}, ${s3Folder2}`);

  // Process first local folder to first S3 folder
  const result1 = await processDirectory(localFolder1, s3Folder1);

  // Process second local folder to second S3 folder
  const result2 = await processDirectory(localFolder2, s3Folder2);

  // Display summary
  console.log('\n=== Upload Summary ===');
  console.log(`${localFolder1} -> ${s3Folder1}:`);
  console.log(`  Total files: ${result1.total}`);
  console.log(`  Successfully uploaded: ${result1.success}`);
  console.log(`  Failed uploads: ${result1.failure}`);

  console.log(`\n${localFolder2} -> ${s3Folder2}:`);
  console.log(`  Total files: ${result2.total}`);
  console.log(`  Successfully uploaded: ${result2.success}`);
  console.log(`  Failed uploads: ${result2.failure}`);

  console.log('\nUpload process completed.');
}

// Run the main function
main().catch(error => {
  console.error('Error:', error.message);
  process.exit(1);
});