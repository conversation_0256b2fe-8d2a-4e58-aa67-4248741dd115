#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import FormData from 'form-data';
import fetch from 'node-fetch';

// Check if a file path is provided
if (process.argv.length < 3) {
  console.error('Please provide a PDF file path');
  console.error('Usage: node test-file-upload.js <pdf-file-path> [endpoint]');
  process.exit(1);
}

// Get the file path from command line arguments
const filePath = process.argv[2];
const endpoint = process.argv[3] || 'process-invoice-file';

// Check if the file exists
if (!fs.existsSync(filePath)) {
  console.error(`File not found: ${filePath}`);
  process.exit(1);
}

// Check if the file is a PDF
if (!filePath.toLowerCase().endsWith('.pdf')) {
  console.error('The file must be a PDF');
  process.exit(1);
}

// Create a form data object
const form = new FormData();

// Add the file to the form data
form.append('file', fs.createReadStream(filePath));

// Add other required fields
form.append('vendor_name', 'Test Vendor');
form.append('invoice_number', 'INV-123');
form.append('invoice_date', '2023-01-01');
form.append('invoice_amount', '1000');
form.append('vat_amount', '100');

// Set the API URL
const apiUrl = `http://localhost:3000/api/${endpoint}`;

console.log(`Uploading file: ${filePath}`);
console.log(`To endpoint: ${apiUrl}`);

// Send the request
(async () => {
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      body: form,
      headers: {
        ...form.getHeaders(),
      },
    });

    // Get the response body
    const data = await response.json();

    // Log the response
    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error);
  }
})();
