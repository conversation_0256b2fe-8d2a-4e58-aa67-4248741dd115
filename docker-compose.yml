version: '3.8'

services:
  app:
    # image: 448938462703.dkr.ecr.ap-southeast-1.amazonaws.com/ext/ki/invoice-matching:latest
    image: invoice-matching:latest
    container_name: invoice-matching
    ports:
      - "3000:3000"
    environment:
      # Node environment
      - NODE_ENV=production
      # Database connection
      - DATABASE_URL=postgresql://postgres.axyujusllpfbbgewabta:<EMAIL>:5432/invoice_matching_staging
      # NextAuth configuration
      - NEXTAUTH_SECRET=kSQlOu+drrdvlgRc/xr+k3UMR7Q+Pt80REBbScSRY8o=
      - NEXTAUTH_URL=http://localhost:3000
      # Document Processing Service
      - DONA_HOST=http://************:8000
      - DONA_API_KEY=m9mOq0v3Qtzx73iekKMLGjnNo7MpqGRN
      # - DONA_HOST=https://dona-api-staging.happyfresh.io
      # - DONA_API_KEY=m9mOq0v3Qtzx73iekKMLGjnNo7MpqGRN
    restart: unless-stopped
    # networks:
    #   - invoice-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    volumes:
      - ./public:/app/public
      - ./uploads:/app/uploads

# networks:
#   invoice-network:
#     driver: bridge
