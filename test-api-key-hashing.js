// Simple test script to verify API key hashing implementation
const crypto = require('crypto');

/**
 * Generate a secure random API key with a prefix and hash
 * @returns An object containing the full API key and its hash
 */
function generateApiKey() {
  // Generate a key with a prefix for identification
  const keyId = 'ki_' + crypto.randomBytes(8).toString('hex');
  const keySecret = crypto.randomBytes(32).toString('hex');
  const fullKey = `${keyId}.${keySecret}`;

  // Hash the key for storage
  const hashedKey = crypto.createHash('sha256').update(fullKey).digest('hex');

  return { fullKey, hashedKey };
}

/**
 * Validate an API key against a stored hash
 * @param {string} apiKey - The API key to validate
 * @param {string} storedHash - The stored hash to compare against
 * @returns {boolean} - Whether the API key is valid
 */
function validateApiKey(apiKey, storedHash) {
  // Hash the provided API key
  const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');

  // Compare the hashes
  return hashedKey === storedHash;
}

// Test the implementation
const { fullKey, hashedKey } = generateApiKey();
console.log('Generated API Key:', fullKey);
console.log('Hashed Key:', hashedKey);

// Validate the API key
const isValid = validateApiKey(fullKey, hashedKey);
console.log('API Key Valid:', isValid);

// Try with an invalid key
const invalidKey = 'ki_12345678.invalid-key';
const isInvalidValid = validateApiKey(invalidKey, hashedKey);
console.log('Invalid Key Valid:', isInvalidValid);
