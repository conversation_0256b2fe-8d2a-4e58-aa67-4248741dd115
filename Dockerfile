# syntax=docker/dockerfile:1

# ===== STAGE 1: Base =====
# Sets up Node.js and pnpm.
# Copies only package manager files to leverage caching for dependency installation.
FROM node:22-alpine AS base
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm@10.8.1

# Copy package files. This layer is cached if these files don't change.
COPY package.json pnpm-lock.yaml ./

# ===== STAGE 2: Dependencies =====
# Installs all dependencies (including dev) needed for building.
FROM base AS deps

# Install all dependencies based on the lockfile.
# This layer is cached if package.json or pnpm-lock.yaml haven't changed.
RUN pnpm install --frozen-lockfile

# ===== STAGE 3: Builder =====
# Builds the application.
FROM deps AS builder
WORKDIR /app

# Set environment variables for build
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
# Consider if 4GB is always needed, could be parameterized via build-arg if it varies
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Copy Prisma schema first, as db:generate depends on it.
# If only other source code changes, this layer and db:generate can be cached.
COPY prisma ./prisma
RUN pnpm db:generate

# Copy the rest of the application code
# This will invalidate the cache for this layer and subsequent layers if any file changes.
COPY . .

# Build the application.
# Ensure your next.config.js has `output: 'standalone'` for this to be most effective.
RUN pnpm build

# (Optional) Prune development dependencies after build if not using standalone,
# or if standalone output still contains too many dev deps (unlikely for node_modules).
# RUN pnpm prune --prod

# ===== STAGE 4: Runner =====
# Creates the final lightweight production image.
FROM node:22-alpine AS runner
WORKDIR /app

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV NEXT_TELEMETRY_DISABLED=1
ENV HOSTNAME=0.0.0.0

# Create a non-root user and group
RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs

# Install ImageMagick and dependencies required for PDF processing
RUN apk add --no-cache imagemagick ghostscript poppler-utils

# Copy Next.js standalone output
# This includes the server, public, static assets, and necessary node_modules.
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Copy the pdfjs-dis from node_modules in the builder stage
COPY --from=builder --chown=nextjs:nodejs /app/node_modules/pdfjs-dist ./node_modules/pdfjs-dist

# Copy Prisma schema IF it's needed at runtime by your application.
# The generated Prisma client (part of .next/standalone) might be enough.
# If your app reads the schema file directly at runtime (e.g., for dynamic connections not from env), then copy it.
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# The commented-out cleanup might not be necessary with `output: 'standalone'`
# as it already optimizes node_modules. If image size is still an issue,
# you could re-evaluate this, but it adds to build time.
# RUN find /app/node_modules ... (if not using standalone fully or for specific cleanups)

# Disable for debugging
USER nextjs

EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# The entrypoint for a standalone Next.js app is typically server.js in the standalone output.
CMD ["node", "server.js"]
