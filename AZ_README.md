```bash
az provider register -n Microsoft.OperationalInsights --wait
```

Step 1: Create an Azure Container Apps Environment (if you don't have one)
Your Container App needs to run within an environment. If you already have one in the kidemo resource group and southeastasia location, you can skip this.

```bash
az containerapp env create \
  --name "$CONTAINER_APP_ENV_NAME" \
  --resource-group "$RESOURCE_GROUP" \
  --location "$LOCATION" \
  --query name -o tsv
```
Wait for the environment creation to complete.

Step 2: Create the Azure Container App (initially without managed identity)
We'll create the Container App first. You can use a dummy public image initially if you prefer, or just specify your private image, knowing the pull will fail until the managed identity is configured.

```bash
echo "Creating Container App: $CONTAINER_APP_NAME..."
az containerapp create \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP" \
  --environment "$CONTAINER_APP_ENV_NAME" \
  --image "mcr.microsoft.com/azuredocs/containerapps-helloworld:latest" `# Using a public image for initial creation` \
  --target-port "$TARGET_PORT" \
  --ingress 'external'
```

Note: If you use your private image (--image "$IMAGE_NAME") here, the deployment will initially fail with a credentials error, but the Container App resource itself will be created. We will fix the image pull in the next steps.

Step 3: Enable System-Assigned Managed Identity for the Container App
This is the corrected step to assign the identity. We use `az containerapp identity assign`.

```bash
echo "Enabling System-Assigned Managed Identity for Container App: $CONTAINER_APP_NAME..."
az containerapp identity assign \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP" \
  --system-assigned \
  --query principalId -o tsv
```

Step 4: Grant the Managed Identity "AcrPull" Permissions on your ACR
Now, assign the `AcrPull` role to the Container App's managed identity on your Azure Container Registry.

```bash
# Assign the AcrPull role to the Container App's Managed Identity
echo "Granting AcrPull role to Container App's Managed Identity on ACR..."
az role assignment create \
  --assignee "76811bc1-7a0f-4d14-9d47-7a48fa7784ff" \
  --role "AcrPull" \
  --scope "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/${RESOURCE_GROUP}/providers/Microsoft.ContainerRegistry/registries/${ACR_NAME}"
```

# --- Ensure your variables are still set correctly from the previous steps ---
RESOURCE_GROUP="kidemo"
CONTAINER_APP_NAME="kim-staging"
ACR_NAME="happyfresh" # The actual ACR resource name, NOT the login server prefix
IMAGE_NAME="${ACR_NAME}.azurecr.io/ext/ki/invoice-matching:latest" # This uses the login server prefix, which is correct for --image

# 1. (Optional, but good for clarity) Confirm the registry is set to use managed identity
#    You already did this in Step 5 of the corrected guide, but it doesn't hurt to ensure.
echo "Confirming registry settings for $CONTAINER_APP_NAME..."
az containerapp registry set \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP" \
  --server "$ACR_NAME.azurecr.io" \
  --identity system

# 2. Update the Container App to use your specific image
echo "Updating Container App $CONTAINER_APP_NAME with image $IMAGE_NAME..."
az containerapp update \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP" \
  --image "$IMAGE_NAME"


Create new registry
# Define variables for your NEW Azure Container Registry
RESOURCE_GROUP="kidemo" # Your existing resource group
LOCATION="southeastasia" # Should ideally match your Container App's location

# !!! IMPORTANT: Choose a globally unique name for your new ACR !!!
# You can try 'happyfresh' if you've confirmed the old one is gone and the name is free.
# Otherwise, consider adding a suffix like 'happyfreshnew', 'happyfreshdev', etc.
ACR_NAME="happyfresh" # <<<--- CHOOSE YOUR NEW, UNIQUE ACR NAME HERE

# SKU: Basic is good for development/testing. Standard offers more features.
ACR_SKU="Basic" # Or "Standard"

echo "Creating new Azure Container Registry: $NEW_ACR_NAME in $RESOURCE_GROUP..."
az acr create \
  --resource-group "$RESOURCE_GROUP" \
  --name "$ACR_NAME" \
  --location "$LOCATION" \
  --sku "$ACR_SKU" \
  --admin-enabled true

CREATE SECRET
```bash
az containerapp secret set \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP" \
  --secrets "db-url=postgresql://kimpgadmin:<EMAIL>:5432/invoice_matching_staging"

  --secrets "db-url=postgresql://postgres.axyujusllpfbbgewabta:<EMAIL>:5432/invoice_matching_staging"

az containerapp secret set \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP" \
  --secrets "nextauth-secret=4iCZTTuad1E/tVcvfrzvkTsOuiDifhbf1oKagUDXy/35qYwC73fHHS7f+uY="

az containerapp secret set \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP" \
  --secrets "dona-api-key=m9mOq0v3Qtzx73iekKMLGjnNo7MpqGRN"
```

```bash
echo "Setting environment variables, referencing the secret..."
az containerapp update \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP" \
  --set-env-vars \
    "NODE_ENV=production" \
    "DONA_HOST=https://dona-api-staging.happyfresh.io" \
    "NEXTAUTH_URL=https://kim-staging.happyfresh.io" \
    "HOSTNAME=0.0.0.0" \
    "DATABASE_URL=secretref:db-url" \
    "NEXTAUTH_SECRET=nextauth-secret \
    "DONA_API_KEY=dona-api-key
```

NODE_ENV="production"
DONA_HOST="https://dona-api-staging.happyfresh.io"
NEXTAUTH_URL="https://kim-staging.happyfresh.io"
HOSTNAME="0.0.0.0"

DATABASE_URL="postgresql://postgres.axyujusllpfbbgewabta:<EMAIL>:5432/invoice_matching_staging"
NEXTAUTH_SECRET=4iCZTTuad1E/tVcvfrzvkTsOuiDifhbf1oKagUDXy/35qYwC73fHHS7f+uY=
DONA_API_KEY="m9mOq0v3Qtzx73iekKMLGjnNo7MpqGRN"




CREATE POSTGRES FLEXIBLE SERVER

Step 1: Define Variables

# Define variables for your PostgreSQL Flexible Server
RESOURCE_GROUP="kidemo" # Your existing resource group
LOCATION="southeastasia" # Should ideally match your Container App's location for lower latency

POSTGRES_SERVER_NAME="kim-postgres-staging" # Choose a unique name for your PostgreSQL server
POSTGRES_ADMIN_USER="kimpgadmin" # Choose a strong admin username (e.g., not 'azureuser' or 'admin')
POSTGRES_ADMIN_PASSWORD="kimZ54pTZ0Ym5pg" # !!! IMPORTANT: Replace with a strong password !!!
                                                  # It must be 8 to 128 characters long and contain characters from three of the following categories:
                                                  # English uppercase letters, English lowercase letters, numbers, and non-alphanumeric characters.

DATABASE_NAME="invoice_matching_staging" # The name of the database for your application

Step 2: Create the PostgreSQL Flexible Server
This command creates the server instance. We'll use a basic SKU for demonstration.
```bash
echo "Creating PostgreSQL Flexible Server: $POSTGRES_SERVER_NAME in $RESOURCE_GROUP..."
az postgres flexible-server create \
  --resource-group "$RESOURCE_GROUP" \
  --name "$POSTGRES_SERVER_NAME" \
  --location "$LOCATION" \
  --admin-user "$POSTGRES_ADMIN_USER" \
  --admin-password "$POSTGRES_ADMIN_PASSWORD" \
  --sku-name Standard_B1ms \
  --tier Burstable \
  --version 16 \
  --storage-size 32 \
  --public-access 0.0.0.0 # Temporarily enable public access to allow Azure services. We'll refine this.
```

Step 3: Create a Database on the Server
Once the server is created, you'll want a specific database for your application.
```bash
echo "Creating database: $DATABASE_NAME on $POSTGRES_SERVER_NAME..."
az postgres flexible-server db create \
  --resource-group "$RESOURCE_GROUP" \
  --server-name "$POSTGRES_SERVER_NAME" \
  --database-name "$DATABASE_NAME"
```

Step 5: Get Connection Information
Once your PostgreSQL server and database are ready, you'll need the connection string details for your Container App.
```
echo "Retrieving PostgreSQL connection details..."
az postgres flexible-server show \
  --resource-group "$RESOURCE_GROUP" \
  --name "$POSTGRES_SERVER_NAME" \
  --query "{host:fullyQualifiedDomainName, user:'$POSTGRES_ADMIN_USER', database:'$DATABASE_NAME'}" \
  --output json
```