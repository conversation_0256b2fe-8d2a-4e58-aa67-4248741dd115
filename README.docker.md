# Docker Setup for Invoice Matching System

This document explains how to use Docker to run the Invoice Matching System in different environments.

## Prerequisites

- Docker and Docker Compose installed on your system
- Git repository cloned to your local machine

## Environment Configuration

1. Copy the example environment file:

```bash
cp .env.example .env
```

2. Edit the `.env` file to set your environment-specific variables:
   - Database credentials
   - NextAuth secret
   - API keys
   - Port mappings

## Available Docker Configurations

The project includes three Docker Compose configurations:

1. **Production** (`docker-compose.yml`): Optimized for production use
2. **Development** (`docker-compose.dev.yml`): For local development with hot-reloading
3. **Testing** (`docker-compose.test.yml`): For running automated tests

## Production Setup

Build and start the production containers:

```bash
docker-compose up -d
```

This will:
- Build the application using the multi-stage Dockerfile
- Start a PostgreSQL database
- Start a pgAdmin instance for database management
- Run the application in production mode

Access the application at http://localhost:3000 (or the port specified in APP_PORT)

## Development Setup

For local development with hot-reloading:

```bash
docker-compose -f docker-compose.dev.yml up -d
```

This will:
- Mount your local code into the container
- Enable hot-reloading for code changes
- Start a PostgreSQL database
- Start a pgAdmin instance for database management

Access the application at http://localhost:3000 (or the port specified in APP_PORT)

## Running Tests

To run the test suite in a containerized environment:

```bash
docker-compose -f docker-compose.test.yml up --build
```

This will:
- Build a test-specific Docker image
- Start a dedicated test database
- Run the test suite
- Exit with the test result code

## Database Management

pgAdmin is included for database management:

1. Access pgAdmin at http://localhost:5050 (or the port specified in PGADMIN_PORT)
2. Login with the credentials specified in your .env file (default: <EMAIL> / admin)
3. Connect to the database server:
   - Host: db (or db-test for test environment)
   - Port: 5432
   - Username: postgres (or as specified in POSTGRES_USER)
   - Password: postgres (or as specified in POSTGRES_PASSWORD)

## Container Management

Common Docker commands:

```bash
# View running containers
docker-compose ps

# View logs
docker-compose logs -f

# Stop containers
docker-compose down

# Rebuild and restart containers
docker-compose up -d --build

# Remove volumes (will delete database data)
docker-compose down -v
```

## Initial Setup

When running the application for the first time, you'll need to create an admin user:

```bash
# For production
docker-compose exec app pnpm create-admin

# For development
docker-compose -f docker-compose.dev.yml exec app pnpm create-admin
```

Follow the prompts to create your admin user.

## Troubleshooting

### Database Connection Issues

If the application can't connect to the database:

1. Check that the database container is running: `docker-compose ps`
2. Verify the database credentials in your .env file
3. Check the database logs: `docker-compose logs db`
4. Try connecting to the database using pgAdmin

### Container Build Issues

If you encounter issues building the containers:

1. Make sure Docker has enough resources allocated
2. Try rebuilding with no cache: `docker-compose build --no-cache`
3. Check for errors in the build logs

### Application Errors

If the application is not working correctly:

1. Check the application logs: `docker-compose logs app`
2. Verify that all environment variables are set correctly
3. Try restarting the containers: `docker-compose restart`
