{"info": {"name": "Invoice Matching API", "description": "API for processing and matching invoices and tax invoices", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0", "_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"}, "item": [{"name": "Invoice Processing", "description": "API endpoints for processing standard invoices", "item": [{"name": "Process Invoice (URL)", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "ki_12345678.your-api-key-secret", "type": "text", "description": "API key for authentication"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"file_url\": \"https://example.com/path/to/invoice.pdf\",\n    \"vendor_name\": \"PT. Parker <PERSON>\",\n    \"invoice_number\": \"69657555\",\n    \"invoice_date\": \"2024-10-31\",\n    \"invoice_amount\": 7674695,\n    \"vat_amount\": 760555\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/process-invoice", "host": ["{{baseUrl}}"], "path": ["api", "process-invoice"]}, "description": "Process a PDF invoice from a URL and match against provided data"}, "response": [{"name": "Successful Response", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "ki_12345678.your-api-key-secret", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"file_url\": \"https://example.com/path/to/invoice.pdf\",\n    \"vendor_name\": \"PT. Parker <PERSON>\",\n    \"invoice_number\": \"69657555\",\n    \"invoice_date\": \"2024-10-31\",\n    \"invoice_amount\": 7674695,\n    \"vat_amount\": 760555\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/process-invoice", "host": ["{{baseUrl}}"], "path": ["api", "process-invoice"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"processing_id\": \"12345678-1234-1234-1234-123456789012\",\n    \"results\": {\n        \"success\": true,\n        \"summary\": {\n            \"total_fields\": 5,\n            \"matched\": 3,\n            \"mismatched\": 1,\n            \"not_found\": 1\n        },\n        \"fields\": {\n            \"vendor_name\": {\n                \"input_value\": \"PT. Parker <PERSON>\",\n                \"ocr_value\": \"PT. Parker Hanfin <PERSON>\",\n                \"status\": \"matched\"\n            },\n            \"invoice_number\": {\n                \"input_value\": \"69657555\",\n                \"ocr_value\": \"69657555\",\n                \"status\": \"matched\"\n            },\n            \"invoice_date\": {\n                \"input_value\": \"2024-10-31\",\n                \"ocr_value\": \"2024-10-31\",\n                \"status\": \"matched\"\n            },\n            \"invoice_amount\": {\n                \"input_value\": \"7674695\",\n                \"ocr_value\": \"7674695.50\",\n                \"status\": \"mismatched\"\n            },\n            \"vat_amount\": {\n                \"input_value\": \"760555\",\n                \"ocr_value\": \"\",\n                \"status\": \"not_found\"\n            }\n        },\n        \"is_colored\": true,\n        \"color_pages\": [1, 3],\n        \"total_pages\": 4\n    },\n    \"log_id\": \"87654321-4321-4321-4321-210987654321\"\n}"}]}, {"name": "Process Invoice File (Upload)", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "ki_12345678.your-api-key-secret", "type": "text", "description": "API key for authentication"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/invoice.pdf", "description": "PDF invoice file to process"}, {"key": "vendor_name", "value": "PT<PERSON>", "type": "text", "description": "Vendor name for matching"}, {"key": "invoice_number", "value": "69657555", "type": "text", "description": "Invoice number for matching"}, {"key": "invoice_date", "value": "2024-10-31", "type": "text", "description": "Invoice date for matching (YYYY-MM-DD)"}, {"key": "invoice_amount", "value": "7674695", "type": "text", "description": "Invoice amount for matching"}, {"key": "vat_amount", "value": "760555", "type": "text", "description": "VAT amount for matching"}]}, "url": {"raw": "{{baseUrl}}/api/process-invoice-file", "host": ["{{baseUrl}}"], "path": ["api", "process-invoice-file"]}, "description": "Upload a PDF invoice file for processing and matching against provided data"}, "response": [{"name": "Successful Response", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "ki_12345678.your-api-key-secret", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/invoice.pdf"}, {"key": "vendor_name", "value": "PT<PERSON>", "type": "text"}, {"key": "invoice_number", "value": "69657555", "type": "text"}, {"key": "invoice_date", "value": "2024-10-31", "type": "text"}, {"key": "invoice_amount", "value": "7674695", "type": "text"}, {"key": "vat_amount", "value": "760555", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/process-invoice-file", "host": ["{{baseUrl}}"], "path": ["api", "process-invoice-file"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"processing_id\": \"12345678-1234-1234-1234-123456789012\",\n    \"results\": {\n        \"success\": true,\n        \"summary\": {\n            \"total_fields\": 5,\n            \"matched\": 3,\n            \"mismatched\": 1,\n            \"not_found\": 1\n        },\n        \"fields\": {\n            \"vendor_name\": {\n                \"input_value\": \"PT. Parker <PERSON>\",\n                \"ocr_value\": \"PT. Parker Hanfin <PERSON>\",\n                \"status\": \"matched\"\n            },\n            \"invoice_number\": {\n                \"input_value\": \"69657555\",\n                \"ocr_value\": \"69657555\",\n                \"status\": \"matched\"\n            },\n            \"invoice_date\": {\n                \"input_value\": \"2024-10-31\",\n                \"ocr_value\": \"2024-10-31\",\n                \"status\": \"matched\"\n            },\n            \"invoice_amount\": {\n                \"input_value\": \"7674695\",\n                \"ocr_value\": \"7674695.50\",\n                \"status\": \"mismatched\"\n            },\n            \"vat_amount\": {\n                \"input_value\": \"760555\",\n                \"ocr_value\": \"\",\n                \"status\": \"not_found\"\n            }\n        },\n        \"is_colored\": true,\n        \"color_pages\": [1, 3],\n        \"total_pages\": 4\n    },\n    \"log_id\": \"87654321-4321-4321-4321-210987654321\"\n}"}]}]}, {"name": "Tax Invoice Processing", "description": "API endpoints for processing tax invoices", "item": [{"name": "Process Tax Invoice (URL)", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "ki_12345678.your-api-key-secret", "type": "text", "description": "API key for authentication"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"file_url\": \"https://example.com/path/to/tax-invoice.pdf\",\n    \"vendor_name\": \"PT. SHIBAURA SHEARING\",\n    \"tax_invoice_number\": \"010.009-24.70808479\",\n    \"tax_invoice_date\": \"2024-11-08\",\n    \"invoice_amount\": 5616000,\n    \"vat_amount\": 617760\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/process-tax-invoice", "host": ["{{baseUrl}}"], "path": ["api", "process-tax-invoice"]}, "description": "Process a PDF tax invoice from a URL and match against provided data"}, "response": [{"name": "Successful Response", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "ki_12345678.your-api-key-secret", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"file_url\": \"https://example.com/path/to/tax-invoice.pdf\",\n    \"vendor_name\": \"PT. SHIBAURA SHEARING\",\n    \"tax_invoice_number\": \"010.009-24.70808479\",\n    \"tax_invoice_date\": \"2024-11-08\",\n    \"invoice_amount\": 5616000,\n    \"vat_amount\": 617760\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/process-tax-invoice", "host": ["{{baseUrl}}"], "path": ["api", "process-tax-invoice"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"processing_id\": \"12345678-1234-1234-1234-123456789012\",\n    \"results\": {\n        \"success\": true,\n        \"summary\": {\n            \"total_fields\": 5,\n            \"matched\": 4,\n            \"mismatched\": 1,\n            \"not_found\": 0\n        },\n        \"fields\": {\n            \"vendor_name\": {\n                \"input_value\": \"PT. SHIBAURA SHEARING\",\n                \"ocr_value\": \"PT. SHIBAURA SHEARING\",\n                \"status\": \"matched\"\n            },\n            \"tax_invoice_number\": {\n                \"input_value\": \"010.009-24.70808479\",\n                \"ocr_value\": \"010.009-24.70808479\",\n                \"status\": \"matched\"\n            },\n            \"tax_invoice_date\": {\n                \"input_value\": \"2024-11-08\",\n                \"ocr_value\": \"2024-11-08\",\n                \"status\": \"matched\"\n            },\n            \"invoice_amount\": {\n                \"input_value\": \"5616000\",\n                \"ocr_value\": \"5616000\",\n                \"status\": \"matched\"\n            },\n            \"vat_amount\": {\n                \"input_value\": \"617760\",\n                \"ocr_value\": \"617700\",\n                \"status\": \"mismatched\"\n            }\n        },\n        \"is_colored\": false,\n        \"color_pages\": [],\n        \"total_pages\": 2\n    },\n    \"log_id\": \"87654321-4321-4321-4321-210987654321\"\n}"}]}, {"name": "Process Tax Invoice File (Upload)", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "ki_12345678.your-api-key-secret", "type": "text", "description": "API key for authentication"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/tax-invoice.pdf", "description": "PDF tax invoice file to process"}, {"key": "vendor_name", "value": "PT. SHIBAURA SHEARING", "type": "text", "description": "Vendor name for matching"}, {"key": "tax_invoice_number", "value": "010.009-24.70808479", "type": "text", "description": "Tax invoice number for matching"}, {"key": "tax_invoice_date", "value": "2024-11-08", "type": "text", "description": "Tax invoice date for matching (YYYY-MM-DD)"}, {"key": "invoice_amount", "value": "5616000", "type": "text", "description": "Invoice amount for matching"}, {"key": "vat_amount", "value": "617760", "type": "text", "description": "VAT amount for matching"}]}, "url": {"raw": "{{baseUrl}}/api/process-tax-invoice-file", "host": ["{{baseUrl}}"], "path": ["api", "process-tax-invoice-file"]}, "description": "Upload a PDF tax invoice file for processing and matching against provided data"}, "response": [{"name": "Successful Response", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "ki_12345678.your-api-key-secret", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/tax-invoice.pdf"}, {"key": "vendor_name", "value": "PT. SHIBAURA SHEARING", "type": "text"}, {"key": "tax_invoice_number", "value": "010.009-24.70808479", "type": "text"}, {"key": "tax_invoice_date", "value": "2024-11-08", "type": "text"}, {"key": "invoice_amount", "value": "5616000", "type": "text"}, {"key": "vat_amount", "value": "617760", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/process-tax-invoice-file", "host": ["{{baseUrl}}"], "path": ["api", "process-tax-invoice-file"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"processing_id\": \"12345678-1234-1234-1234-123456789012\",\n    \"results\": {\n        \"success\": true,\n        \"summary\": {\n            \"total_fields\": 5,\n            \"matched\": 4,\n            \"mismatched\": 1,\n            \"not_found\": 0\n        },\n        \"fields\": {\n            \"vendor_name\": {\n                \"input_value\": \"PT. SHIBAURA SHEARING\",\n                \"ocr_value\": \"PT. SHIBAURA SHEARING\",\n                \"status\": \"matched\"\n            },\n            \"tax_invoice_number\": {\n                \"input_value\": \"010.009-24.70808479\",\n                \"ocr_value\": \"010.009-24.70808479\",\n                \"status\": \"matched\"\n            },\n            \"tax_invoice_date\": {\n                \"input_value\": \"2024-11-08\",\n                \"ocr_value\": \"2024-11-08\",\n                \"status\": \"matched\"\n            },\n            \"invoice_amount\": {\n                \"input_value\": \"5616000\",\n                \"ocr_value\": \"5616000\",\n                \"status\": \"matched\"\n            },\n            \"vat_amount\": {\n                \"input_value\": \"617760\",\n                \"ocr_value\": \"617700\",\n                \"status\": \"mismatched\"\n            }\n        },\n        \"is_colored\": false,\n        \"color_pages\": [],\n        \"total_pages\": 2\n    },\n    \"log_id\": \"87654321-4321-4321-4321-210987654321\"\n}"}]}]}]}