stages:
  - test

variables:
  # Use TLS https://docs.gitlab.com/ee/ci/docker/using_docker_build.html#tls-enabled
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: "/certs"
  # Optimize CI performance
  DOCKER_DRIVER: overlay2
  # Use pnpm as package manager
  PNPM_VERSION: 10.8.1
  # Next.js specific
  NEXT_TELEMETRY_DISABLED: 1
  # Docker image name
  IMAGE_NAME: $CI_REGISTRY_IMAGE

# Cache dependencies between jobs
.node_modules_cache: &node_modules_cache
  key:
    files:
      - pnpm-lock.yaml
  paths:
    - node_modules/
  policy: pull-push

# Cache pnpm store
.pnpm_cache: &pnpm_cache
  key: pnpm-store
  paths:
    - .pnpm-store
  policy: pull-push

# Cache Next.js build cache
.next_cache: &next_cache
  key: next-cache
  paths:
    - .next/cache
  policy: pull-push

# Base job for Node.js tasks
.node_base:
  image: node:22-alpine
  before_script:
    - npm install -g pnpm@$PNPM_VERSION
    - pnpm config set store-dir .pnpm-store

# Test job
test:
  extends: .node_base
  stage: test
  cache:
    - <<: *pnpm_cache
    - <<: *node_modules_cache
  variables:
    # Add mock environment variables for testing
    DPS_HOST: "https://mock-dps-host.example.com"
    DPS_API_KEY: "mock-api-key-for-testing"
  script:
    - pnpm install --frozen-lockfile
    - pnpm db:generate
    - pnpm test:run
  artifacts:
    reports:
      junit: junit.xml
    when: always
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Build and push Docker image
# build:
#   stage: build
#   image: docker:24.0.7
#   services:
#     - docker:24.0.7-dind
#   cache:
#     - <<: *next_cache
#   variables:
#     # Use buildkit for more efficient builds
#     DOCKER_BUILDKIT: 1
#   before_script:
#     - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
#   script:
#     # Build the Docker image
#     - |
#       docker build \
#         --cache-from $IMAGE_NAME:latest \
#         --tag $IMAGE_NAME:$CI_COMMIT_SHA \
#         --tag $IMAGE_NAME:latest \
#         .
#     # Push the Docker image to the GitLab registry
#     - docker push $IMAGE_NAME:$CI_COMMIT_SHA
#     - docker push $IMAGE_NAME:latest
#     # If this is a tag, also tag the image with the tag name
#     - |
#       if [[ $CI_COMMIT_TAG ]]; then
#         docker tag $IMAGE_NAME:$CI_COMMIT_SHA $IMAGE_NAME:$CI_COMMIT_TAG
#         docker push $IMAGE_NAME:$CI_COMMIT_TAG
#       fi
#   rules:
#     - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
#     - if: $CI_COMMIT_TAG

# Deploy to staging (example, uncomment if needed)
# deploy_staging:
#   stage: deploy
#   image: alpine:latest
#   script:
#     - echo "Deploy to staging server"
#     # Add your deployment commands here
#   environment:
#     name: staging
#   rules:
#     - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Deploy to production (example, uncomment if needed)
# deploy_production:
#   stage: deploy
#   image: alpine:latest
#   script:
#     - echo "Deploy to production server"
#     # Add your deployment commands here
#   environment:
#     name: production
#   rules:
#     - if: $CI_COMMIT_TAG
#   when: manual
