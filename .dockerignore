# Ignore development and git files
.git
.github
.gitignore
.vscode
.idea
*.md
!README.md

# Ignore node modules and build artifacts
node_modules
.next/cache
out
build
coverage
.vercel

# Ignore environment files
.env*
!.env.example

# Ignore logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Ignore test files
tests
__tests__
*.test.ts
*.test.tsx
vitest.config.*

# Ignore Docker files
Dockerfile
.dockerignore

# Ignore temporary files
.DS_Store
*.swp
*.swo
.cache
temp
tmp

# Ignore other unnecessary files
.eslintrc*
.prettierrc*
tsconfig.tsbuildinfo
